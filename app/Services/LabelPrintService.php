<?php

namespace App\Services;

use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Constants\RefundSubStatusConst;
use App\Exceptions\ApiException;
use App\Exceptions\ErrorCodeException;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Shop;
use App\Models\User;
use App\Services\Order\OrderServiceManager;
use App\Services\ShengYiWang\ShengYiWangClient;
use Illuminate\Support\Facades\Log;

class LabelPrintService
{

    /**
     * @throws \Throwable
     * @throws ErrorCodeException
     */
    public function getOrderDetail($user_id, $shop_id, $takeItemCode, $endTime)
    {

        $user = User::find($user_id);
        // 拉取 syw 的订单和货品
        $shengYiWangClient = new ShengYiWangClient();
        $params = [
            'takeItemCode' => $takeItemCode,
        ];
        $shengYiWangClient->setUser($user);
        $response = $shengYiWangClient->execute('POST', '/third/print/order/detial', $params);
        $shengYiWangClient->handleResponse($response);
        $responseData = $response['data'];

        Log::info('responseData',["takeItemCode"=>$takeItemCode,"response"=>$responseData]);
        if (empty($responseData)){
            throw new \Exception('订单信息获取失败');
        }
        $platformType = PlatformConst::SYW_TYPE_MAP[$responseData['sourceType']];
        $shopUid = $responseData['shopUId'];
        $orderShop = Shop::where(['identifier' => $shopUid, 'type' => $platformType])->first();
        if(empty($orderShop)){
            throw new ApiException(ErrorConst::SHOP_NOT_FOUND);
        }
        // 拉平台的订单信息接口保存
        $platform = PlatformConst::PLATFORM_TYPE_MAP_REVERT[$platformType];
        $orderService = OrderServiceManager::create($orderShop->getPlatform());
        $orderService->setShop($orderShop);
        $tid = $responseData['sourceNo'];
        $orderParams = ['id'=> $tid,'tid'=> $tid];
        $tradesOrders = $orderService->batchGetOrderInfo([$orderParams]);
        if (empty($tradesOrders) || empty($tradesOrders[0])){
            throw new \Exception('拉取平台订单失败');
        }
        Order::batchSave($tradesOrders, $orderShop->user_id, $orderShop->id,$platform);
        $orderQuery = \App\Models\Fix\Order::query()->where('tid', $tid);

        $orderItemMergeQuery = \App\Models\Fix\OrderItem::query()
            ->leftJoin('orders', 'orders.id', 'order_items.order_id');
//            ->where('order_items.status', OrderItem::ORDER_STATUS_PAYMENT);
        if (!empty($endTime)){
            $orderItemMergeQuery->where('orders.pay_at','>', $endTime);
            $orderQuery->where('orders.pay_at','>', $endTime);
        }
        $order = $orderQuery->firstOrFail();

        $orderArr = $order->toArray();
        $orderArr['printItems'] = $responseData['items'];
        $responseOrderItem = array_first($orderArr['printItems']);

        $orderItem = \App\Models\Fix\OrderItem::query()
            ->with(['packages.printPackageOrders', 'order', 'order.orderCipherInfo'])
            ->where('order_id', $orderArr['id'])
            ->where('sku_id', $responseOrderItem['relationSkuId'])->firstOrFail();

        // 订单已发货逻辑
        if ($orderItem['status'] >= OrderItem::ORDER_STATUS_DELIVERED){
            $package = array_first($orderItem['packages']);
            if (empty($package)){
                $mergeItemIdArr = [];
            }else{
                $mergeItemIdArr = $package->printPackageOrders->pluck('order_item_id')->toArray();
            }
            $mergeItemIdArr[] = $orderItem['id']; // 加上当前 itemId
//            $mergeItemIdArr = array_diff($mergeItemIdArr, [$orderItem['id']]);  // 去掉当前 itemId
            $mergeItems = $orderItemMergeQuery->whereIn('order_items.id', $mergeItemIdArr)
                ->where('orders.shop_id', $order['shop_id'])
                ->whereNotIn('order_items.refund_sub_status', RefundSubStatusConst::REFUND_COMPLETE_ARRAY) // 排除退款完成
                ->get(['order_items.*']);
        }else{
            // 查出合单订单
            $mergeItems = $orderItemMergeQuery->where('receiver_phone', $order['receiver_phone'])
//            ->where('order_items.id', '!=', $orderItem->id)
                ->where('order_items.status', OrderItem::ORDER_STATUS_PAYMENT)
                ->where('orders.shop_id', $order['shop_id'])
                ->get(['order_items.*']);
        }

        $tidArr = $mergeItems->pluck('tid')->toArray();
        $orderItemProductItems = [];
        $mergeItemsArr = [];
        if (!empty($tidArr)) {
            $params = [
                'shopOrderNos' => $tidArr,
            ];
            $responseList = [];
            try {
                // 拉取合单订单详情
                $responseList = $shengYiWangClient->execute('POST', '/third/print/order/listDetail', $params);
                $shengYiWangClient->handleResponse($responseList);
                Log::info('订单详情列表',[$params,$responseList]);
                foreach ($mergeItems as $index => $mergeItem) {
                    // 去掉当前的订单
                    if ($mergeItem['id'] == $orderItem->id) {
                        unset($mergeItems[$index]);
                        continue;
                    }
                    $firstOrder = collect($responseList['data'])->where('sourceNo', $mergeItem->tid)->first();
                    $firstItem = collect($firstOrder['items'])->where('relationSkuId', $mergeItem->sku_id)->first();
                    $mergeItem->productItems = $this->handProductItems( $firstItem['relationItems'] ?? []);
                }
                $firstOrder = collect($responseList['data'])->where('sourceNo', $orderItem->tid)->first();
                $firstItem = collect($firstOrder['items'])->where('relationSkuId', $orderItem->sku_id)->first();
                $orderItemProductItems = $firstItem['relationItems'] ?? [];
                $mergeItemsArr = $mergeItems->values()->toArray();
            }catch (\Exception $e){
                Log::error('订单详情列表失败', [$params, $responseList]);
                $orderItemProductItems = $responseOrderItem['relationItems'] ?? [];
            }
        }

        $orderItem->mergeCount = count($mergeItemsArr);
        $orderItem->mergeItems = $mergeItemsArr;
        $orderItem->productItems = $this->handProductItems($orderItemProductItems);
        return $orderItem;
    }

    private function handProductItems($orderItemProductItems)
    {
        $list = [];
        if (empty($orderItemProductItems)) {
            return $list;
        }
        // 根据takItemCode 拆分成多个
        foreach ($orderItemProductItems as $index => $orderItemProductItem) {
            foreach ($orderItemProductItem['takeItemCode'] as $index1 => $takItemCode) {
                $orderItemProductItem['takeItemCode'] = $takItemCode;
                $list[] = $orderItemProductItem;
            }
        }
        return $list;
    }
}
