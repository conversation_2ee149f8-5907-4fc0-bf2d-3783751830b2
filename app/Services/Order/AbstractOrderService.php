<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/2/21
 * Time: 19:43
 */

namespace App\Services\Order;


use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Events\Event;
use App\Exceptions\ErrorCodeException;
use App\Exceptions\OrderException;
use App\Exceptions\ShopAuthExpiredException;
use App\Http\StatusCode\StatusCode;
use App\Jobs\Goods\SyncGoodsJob;
use App\Jobs\Orders\DeleteHistoryOrderJob;
use App\Jobs\Orders\SyncOrdersJob;
use App\Jobs\Orders\SyncHistoryOrdersJob;
use App\Jobs\Users\SaveUserEdition;
use App\Models\Address;
use App\Models\ApiShopBind;
use App\Models\Order;
use App\Models\PlatformOrder;
use App\Models\Shop;
use App\Models\ShopBind;
use App\Models\ShopExtra;
use App\Models\User;
use App\Models\UserExtra;
use App\Services\Bo\OrderResponseBo;
use App\Services\CommonResponse;
use App\Services\Order\Request\OrderDeliverAgainRequest;
use App\Services\Order\Request\OrderDeliveryRequest;
use App\Services\Order\Request\ReportOrderSecurityEventRequest;
use App\Utils\ArrayUtil;
use App\Utils\Environment;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use InvalidArgumentException;
use Symfony\Component\Translation\Exception\NotFoundResourceException;
use Workerman\Connection\AsyncTcpConnection;
use Workerman\Worker;
use Carbon\Carbon;

use GuzzleHttp\Client;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

abstract class AbstractOrderService
{

    /**
     * 订单状态
     * @var array
     */
    protected $orderStatusMap = [];
    /**
     * 退款状态
     * @var array
     */
    protected $refundStatusMap = [];
    /**
     * 订单旗帜
     * @var array
     */
    protected $orderFlagMap = [];
    /**
     * 平台类型
     * @var string
     */
    protected $platformType = '';
    /**
     * 用户id
     * @var
     */
    protected $userId;
    /**
     * 每次拉取订单间隔的分钟
     * @var int
     */
    public $orderTimeInterval = 0;

    /**
     * 每次拉取增量订单间隔的分钟
     * @var int
     */
    public $orderIncrTimeInterval = 0;

    /**
     * 每次拉取退款订单间隔的分钟
     * @var int
     */
    public $refundOrderTimeInterval = 0;

    /**
     * 每次拉取厂家订单间隔的分钟
     * @var int
     */
    public $factoryOrderTimeInterval = 0;

    /**
     * 是否存在下一页
     * @var bool
     */
    public $hasNext = false;

    /**
     * 微信下一页的key
     * @var string
     */
    public $nextKey = '';

    /**
     * 商品总数
     * @var int
     */
    public $goodsTotalCount = -1;

    /**
     * 订单总数
     * @var int
     */
    private $orderTotalCount = -1;

    /**
     * 请求错误数据格式
     * @var array
     */
    protected $errorInfo
        = [
            'code' => 0,
            'info' => [],
        ];

    //错误码合集
    public $errorCodeArr = [
        self::ERROR_CODE_UNBIND,
        self::ERROR_CODE_SERVER,
    ];

    const ERROR_CODE_UNBIND = 1000; //取消授权
    const ERROR_CODE_SERVER = 1001; //服务错误信息

    protected $page = 1;
    protected $pageSize = 100;
    protected $pageTotal = 0;
    protected $page_cursor = '';
    protected $websocketUrl = '';

    protected $accessToken;
    protected $serviceId;
    protected $specificationId;

    /**
     * @var ?Shop 店铺信息
     */
    protected $shop;

    protected $dataType = 'JSON';
    protected $concurrency = 50;  //并发数
    /**
     * curl 并发池异常输出原始数据
     * @var bool
     */
    protected $poolCurlAbnormalOutputOriginalData = false;


    public function __construct()
    {

    }

    /**
     * 格式化成订单表结构
     * @param array $trade
     * @return array
     * <AUTHOR>
     */
    abstract public function formatToOrder(array $trade): array;

    abstract public function formatToAfterSale(array $trade);

    /**
     * 批量格式化成订单表结构
     * @param array $orders
     * @return array
     * <AUTHOR>
     */
    abstract public function formatToOrders(array $orders): array;

    /**
     * 批量格式化成商品表结构
     * @param array $goods
     * @return array
     * <AUTHOR>
     */
    abstract public function formatToGoods(array $goods): array;

    /**
     * 发送获取订单列表请求
     * 通过创建时间或者支付时间查询
     * @param  int  $startTime
     * @param  int  $endTime
     * @param  bool  $isFirstPull 现在这个字段是意思是 是否拉取待发货
     * @return array
     * @throws OrderException
     * <AUTHOR>
     */
    abstract protected function sendGetTradesOrders(int $startTime, int $endTime, bool $isFirstPull = false);

    /**
     * 发送获取订单详情请求
     * @param string $tid
     * @return mixed
     */
    abstract protected function sendGetOrderInfo(string $tid);

    abstract protected function sendGetAfterSaleOrderInfo(string $tid);

    /**
     * 发送获取商品列表请求
     * @param int $pageSize
     * @param int $currentPage
     * @return mixed
     */
    abstract protected function sendGetGoods(int $pageSize, int $currentPage);

//    abstract protected  function sendGetGoodsById()

    /**
     * 订单发货
     * @param string $tid
     * @param string $expressCode 快递公司编码
     * @param string $expressNo 快递单号
     * @param array $orderItemOId
     * @param bool $silent
     * @return mixed
     */
    abstract protected function deliverySellerOrders(string $tid, string $expressCode, string $expressNo, array $orderItemOId, bool $silent = true);

    /**
     * 重新订单发货
     * @param OrderDeliverAgainRequest $orderDeliverAgainRequest
     * @return bool
     * @throws ErrorCodeException
     */
    protected function deliverySellerOrdersAgain(OrderDeliverAgainRequest $orderDeliverAgainRequest): bool
    {
        throw_error_code_exception(StatusCode::OPERATION_FAILED);
    }

    /**
     * 订单发货
     * @param string $tid
     * @param string $expressCode
     * @param string $expressNo
     * @return mixed
     */
    abstract protected function deliverySellerOrdersForOpenApi(string $tid, string $expressCode, string $expressNo, array $orderItemOId);

    /**
     * 发送获取订单列表请求 订单增量
     * @param int $startTime
     * @param int $endTime
     * @param bool $isFirstPull
     * @return array
     * @throws OrderException
     * <AUTHOR>
     */
    abstract protected function sendGetTradesOrdersByIncr(int $startTime, int $endTime, bool $isFirstPull = false): array;

    /**
     * 发送服务订购消息
     * $result = [
     * 'user_id' => $shop->user_id,
     * 'shop_id' => $shop->id,
     * 'identifier' => $shop->identifier,
     * 'platform_type' => $this->platformType,
     * 'expired_at' => $response['data']['expire_time'], //暂定免费版使用30天
     * 'version' => $version,
     * 'version_desc' => array_get(UserExtra::VERSION_MAP_ARR, $version, ''),
     * 'pay_amount' => 0,
     * ];
     * @return array{user_id:int,shop_id:int,identifier:string,platform_type:int,expired_at:string,version:string,version_desc:string,pay_amount:int}
     * <AUTHOR>
     */
    abstract public function sendServiceInfo(): array;
    /**
     * 发送绑定店铺的服务订购消息
     * @return mixed
     * <AUTHOR>
     */
    //abstract protected function sendBindUserServiceInfo($shop);


    /**
     * 批量解密
     * @param $order
     * @return mixed
     */
    abstract protected function sendDecrypt($order);

    /**
     * 批量加密
     * @param $order
     * @return mixed
     */
    abstract protected function sendBatchEncrypt($order);

    /**
     * 检查授权状态
     * @return bool
     * <AUTHOR>
     */
    abstract public function checkAuthStatus(): bool;

    /**
     * 发货
     * @param string $tid
     * @param string $expressCode
     * @param string $expressNo
     * @param array $orderItemOId
     * @param bool $silent
     * @return mixed
     */
    public function deliveryOrders(string $tid, string $expressCode, string $expressNo, array $orderItemOId = [], bool $silent = true)
    {
        if (empty($this->platformType)) {
            throw new InvalidArgumentException('未定义的字段：authType');
        }

        return $this->deliverySellerOrders($tid, $expressCode, $expressNo, $orderItemOId, $silent);
    }

    /**
     * 批量发货
     * @param OrderDeliveryRequest[] $orderDeliveryRequests
     * @return CommonResponse[]
     */
    abstract public function batchDeliveryOrders(array $orderDeliveryRequests): array;

    /**
     * 批量虚拟发货
     * @param OrderDeliveryRequest[] $orderDeliveryRequests
     * @return CommonResponse[]
     */
    public function batchVirtualDeliveryOrders(array $orderDeliveryRequests): array
    {
        throw new InvalidArgumentException('不支持虚拟发货');
    }


    /**
     * 模拟批量发货成功，这个是为预发货用，预发货就是假定发货都是成功的。
     * @param array $orderDeliveryRequests
     * @return array
     */
    function mockBatchDeliveryOrdersSuccess(array $orderDeliveryRequests): array
    {

        $responses = [];
        foreach ($orderDeliveryRequests as $request) {
            $response = new CommonResponse();
            $response->setRequest($request);
            $response->setSuccess(true);
//            $response->setData($request);
            $responses[] = $response;
        }
        Log::info('模拟批量发货成功', $responses);
        return $responses;
    }

    /**
     * 订单的拆包发货
     * $orderDeliveryRequests的格式
     * [
     *  [
     *  "tid"=>"6919675406348523072A",
     *   "shopId"=>1111,
     *  "packs" =>[
     *          [              "waybillCode"=>"6919675406348588608",
     *                  "expressCode"=>"xxx",
     *                  "goods"=>["oid"=>111,
     *                  "shippedNum"=>1],
     *                   ]
     *  ]
     * ]
     * @param int $shopId
     * @param array{
     *     tid:string,
     *     shopId:int,
     *     packs:array{
     *         waybillCode:string,
     *         expressCode:string,
     *         goods:array{
     *             oid:string,
     *             shippedNum:int
     *         }
     *     }
     * }[] $orderDeliveryRequests
     * @return array{
     *     successes:array{
     *      tid:string  ,
     *      shopId:int,
     *      waybillCodes:string[],
     *     },
     *     failures:array{
     *     tid:string,
     *     shopId:int,
     *     waybillCodes:string[],
     *     msg:string
     *     }
     * }
     * @throws ErrorCodeException
     */
    public function orderMultiPackagesDelivery(int   $shopId,
                                               array $orderDeliveryRequests): array
    {
        throw_error_code_exception(StatusCode::OPERATION_FAILED);
    }

    /**
     * 模拟订单拆包发货成功，主要是给预发货用
     * 返回的结果参考 orderMultiPackagesDelivery
     *
     * @param int $shopId
     * @param array $orderDeliveryRequests
     * @return array
     */
    public function mockOrderMultiPackagesDeliverySuccess(int $shopId, array $orderDeliveryRequests): array
    {

        $successes = [];
        foreach ($orderDeliveryRequests as $request) {
            $tid = $request['tid'];
            $waybillCodes = [];

            $newPacks = [];
            foreach ($request['packs'] as $pack) {
                $waybillCode = $pack['waybillCode'];
                $expressCode = $pack['expressCode'];
                foreach ($pack['goods'] as $good) {
                    $newPacks[] = [
                        'waybillCode' => $waybillCode,
                        'expressCode' => $expressCode,
                        'oid' => $good['oid'],
                        'shippedNum' => $good['shippedNum'],
                    ];
                }
                $waybillCodes[] = ["waybillCode" => $waybillCode, "expressCode" => $expressCode, "packs" => $newPacks];
            }

            $successes[] = ['tid' => $tid, "shopId" => $shopId, "waybillCodes" => $waybillCodes];
        }
        return [
            'successes' => $successes,
            'failures' => []
        ];
    }

    /**
     * 订单追加包裹
     * @param string $tid
     * @param string $wpCode
     * @param string $waybillCode
     * @param array $goodsList [{"oid":111,"num":1}]
     * @return bool
     * @throws ErrorCodeException
     */
    public function orderAppendPackages(string $tid, string $wpCode, string $waybillCode, array $goodsList = []): bool
    {
        throw_error_code_exception(StatusCode::METHOD_NOT_IMPLEMENTED);
    }

    /**
     * 重新订单发货
     * @param OrderDeliverAgainRequest $orderDeliverAgainRequest
     * @return bool
     * @throws ErrorCodeException
     */
    public function deliveryOrdersAgain(OrderDeliverAgainRequest $orderDeliverAgainRequest): bool
    {

        Log::info('重新订单发货', func_get_args());
        if (empty($this->platformType)) {
            throw new InvalidArgumentException('未定义的字段：authType');
        }

        return $this->deliverySellerOrdersAgain($orderDeliverAgainRequest);
    }

    /**
     * 发货
     * @param string $tid
     * @param string $expressCode
     * @param string $expressNo
     * @return mixed
     */
    public function deliveryOrdersForOpenApi(string $tid, string $expressCode, string $expressNo, array $orderItemOId = [])
    {
        if (empty($this->platformType)) {
            throw new InvalidArgumentException('未定义的字段：authType');
        }

        return $this->deliverySellerOrdersForOpenApi($tid, $expressCode, $expressNo, $orderItemOId);
    }

    /**
     * @param mixed $accessToken
     * @return AbstractOrderService
     */
    public function setAccessToken($accessToken): AbstractOrderService
    {
        $this->accessToken = $accessToken;
        return $this;
    }

    /**
     * @param mixed $userId
     */
    public function setUserId($userId): void
    {
        $this->userId = $userId;
    }

    /**
     * @param \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|null $shop
     */
    public function setShop($shop): void
    {
        $this->shop = $shop;
        if (isset($shop->access_token)) {
            $this->setAccessToken($shop->access_token);
        }
    }

    /**
     * @return int
     */
    public function getPage(): int
    {
        return $this->page;
    }

    /**
     * @param int|string $page
     */
    public function setPage($page): void
    {
        $this->page = $page;
    }

    /**
     * @return int
     */
    public function getPageSize(): int
    {
        return $this->pageSize;
    }

    /**
     * 根据时间间隔 计算循环次数
     * @param string $beginAt
     * @param string $endAt
     * @param int $orderIncrTimeInterval
     * @return false|float
     * <AUTHOR>
     */
    public function calcLenByTimeRange(string $beginAt, string $endAt, int $orderIncrTimeInterval)
    {
        return ceil((strtotime($endAt) - strtotime($beginAt)) / 60 / $orderIncrTimeInterval);
    }

    /**
     * @param int $pageSize
     */
    public function setPageSize(int $pageSize): void
    {
        $this->pageSize = $pageSize;
    }

    /**
     * @param int $pageSize
     */
    public function setPlatform(bool $platform): void
    {
        $this->platform = $platform;
    }

    /**
     * @param string $page_cursor
     */
    public function setPageCursor(string $page_cursor): void
    {
        $this->page_cursor = $page_cursor;
    }

    /**
     * @return string
     */
    public function getPageCursor(): string
    {
        return $this->page_cursor;
    }

    /**
     * @return int
     */
    public function getOrderTotalCount(): int
    {
        return $this->orderTotalCount;
    }

    /**
     * @param int $orderTotalCount
     */
    public function setOrderTotalCount(int $orderTotalCount): void
    {
        $this->orderTotalCount = $orderTotalCount;
    }

    public function buildAddressMd5($order, $cipherInfo, $isCrossShopMergeOrder,$oldOrder): string
    {
        $md5_receiver_address = filterZeroChar($order['receiver_address']);
        $addressArr = [
            $order['shop_id'],
            $order['buyer_id'],
            $order['buyer_nick'],
            $order['receiver_state'],
            $order['receiver_city'],
            $order['receiver_district'],
            $md5_receiver_address,
            $order['receiver_name'],
            $order['receiver_phone'],
        ];
        if ($isCrossShopMergeOrder) {
            array_shift($addressArr); // 跨店合单，不需要店铺id
        }
        if ($this->isDy()  && array_get($order, 'is_give_gift', 0) == 1) {
            //如果是抖音,而且为赠送礼物，$addressArr增加一个订单ID，防止合单
            $addressArr[] = $order['tid'];
            Log::info('抖音赠送礼物，增加订单ID到md5地址中', ['tid' => $order['tid']]);
        }
        return md5(implode(',', $addressArr));
    }


    /**
     * 获取客户端
     * @return mixed
     * <AUTHOR>
     */
    abstract protected function getClient();

    /**
     * 开启订阅消息
     * @return bool
     * <AUTHOR>
     */
    abstract public function openSubscribeMsg(): bool;

    /**
     * 消费订阅消息
     * @return array
     * @throws OrderException
     * <AUTHOR>
     */
    abstract public function consumeSubscribeMsg();

    /**
     * 确认订阅消息
     * @param array $idArr
     * @return array
     * @throws OrderException
     * <AUTHOR>
     */
    abstract public function confirmSubscribeMsg(array $idArr);

    /**
     * 处理订单的订阅消息
     * @param $data
     * @return array
     * <AUTHOR>
     */
    abstract public function handleSubscribeMsg($data);

    /**
     * 创建WebsocketServer服务
     * @return AsyncTcpConnection
     * @throws OrderException
     * <AUTHOR>
     */
    abstract public function createWebsocketConnection();


    /**
     * 拉取平台退款审核单列表
     * @param $data
     * @return array
     */
    abstract public function batchGetRefundApplyList($data);

    /**
     * 转换订单状态
     * @param $status
     * @return int
     * <AUTHOR>
     */
    public function formatOrderStatus($status): int
    {
        if (!isset($this->orderStatusMap[$status])) {
            throw new InvalidArgumentException('未定义的订单状态：' . $status);
        }
        return $this->orderStatusMap[$status];
    }

    /**
     * 转换退款状态
     * 只需要定义没有退款状态
     * @param $status
     * @return int
     * <AUTHOR>
     */
    public function hasRefundStatus($status): int
    {
        if (empty($this->refundStatusMap)) {
            throw new InvalidArgumentException('未定义退款状态！');
        }
        if (isset($this->refundStatusMap[$status])) {
            if ($this->refundStatusMap[$status] > 0) {
                return Order::REFUND_STATUS_YES;
            } else {
                return Order::REFUND_STATUS_NO;
            }
        }
        return Order::REFUND_STATUS_YES;
    }

    /**
     * 转换订单旗帜
     * @param int|null $flag
     * @return string
     * <AUTHOR>
     */
    public function formatOrderFlag(?int $flag): string
    {
        if (!isset($flag)) {
            return Order::FLAG_GRAY;
        }
        if (!isset($this->orderFlagMap[$flag])) {
            throw new InvalidArgumentException('未定义的订单旗帜：' . $flag);
        }
        return $this->orderFlagMap[$flag];
    }

    /**
     * 获取交易订单
     * @param int $startTime
     * @param int $endTime
     * @param bool $isFirstPull
     * @return array
     * @throws OrderException
     * <AUTHOR>
     */
    public function getTradesOrder(int $startTime, int $endTime, bool $isFirstPull = false)
    {
        if (empty($this->platformType)) {
            throw new InvalidArgumentException('未定义的字段：authType');
        }

        $this->hasNext = false;
        $res = $this->sendGetTradesOrders($startTime, $endTime, $isFirstPull);
        return $this->formatToOrders($res);
    }

    /**
     * 获取交易订单详情
     * @param string $tid
     * @return array
     */
    public function getOrderInfo(string $tid)
    {
        if (empty($this->platformType)) {
            throw new InvalidArgumentException('未定义的字段：authType');
        }

        $res = $this->sendGetOrderInfo($tid);
        //错误情况处理
        if (isset($res['code']) && in_array($res['code'], $this->errorCodeArr)) {
            if ($res['code'] == self::ERROR_CODE_UNBIND) {
//                \Log::info("授权改成失效", [$this->shop->id]);
                Shop::query()->where('id', $this->shop->id)
                    ->update([
                        'auth_status' => Shop::AUTH_STATUS_ABNORMAL_EXPIRE,
                    ]);
                Log::info('用户授权失效:getOrderInfo', ['shop_id' => $this->shop->id]);
            }
            return [];
        }

        return $this->formatToOrder($res);
    }

    public function getRawOrderInfo(string $tid)
    {
        if (empty($this->platformType)) {
            throw new InvalidArgumentException('未定义的字段：authType');
        }

        return $this->sendGetOrderInfo($tid);
    }

    public function getAfterSaleOrderInfo(string $tid)
    {
        if (empty($this->platformType)) {
            throw new InvalidArgumentException('未定义的字段：authType');
        }
        $res = $this->sendGetAfterSaleOrderInfo($tid);

        return $this->formatToAfterSale($res);
    }

    /**
     * 获取商品列表
     * @param int $pageSize
     * @param int $currentPage
     * @return array
     */
    public function getGoodsList(int $pageSize, int $currentPage = 1)
    {
        if (empty($this->platformType)) {
            throw new InvalidArgumentException('未定义的字段：authType');
        }
        $this->hasNext = false;
        $res = $this->sendGetGoods($pageSize, $currentPage);
//        Log::info('goods_result_' ,$res);
        return $this->formatToGoods($res);
    }

//    public function getGoods()
//    {
//        if (empty($this->platformType)) {
//            throw new InvalidArgumentException('未定义的字段：authType');
//        }
//        $res = $this->sendGetGoods($pageSize, $currentPage);
//    }

    /**
     * 获取交易订单 订单增量
     * @param int $startTime
     * @param int $endTime
     * @param bool $isFirstPull
     * @return array
     * @throws OrderException
     * <AUTHOR>
     */
    public function getTradesOrderByIncr(int $startTime, int $endTime, bool $isFirstPull = false): array
    {
        if (empty($this->platformType)) {
            throw new InvalidArgumentException('未定义的字段：authType');
        }

        $res = $this->sendGetTradesOrdersByIncr($startTime, $endTime, $isFirstPull);
        //错误情况处理
        if (isset($res['code']) && in_array($res['code'], $this->errorCodeArr)) {
            return $res;
        }
        return $this->formatToOrders($res);
    }

    /**
     * 获取token
     * @return mixed
     * @throws ApiException
     * <AUTHOR>
     */
    public function getAccessToken()
    {
        if (!empty($this->accessToken)) {
            return $this->accessToken;
        }
        // 每次都重新获取token
        $shop = $this->getShop();
        return $this->accessToken = $shop->access_token;
    }

    /**
     * 获取用户授权
     * @return Builder|Model|object|null
     * @throws ApiException
     * <AUTHOR>
     */
    public function getShop(): Shop
    {
//        if (empty($this->shop)) {
////            $this->shop = Shop::query()
////                ->where('type', $this->platformType)
////                ->where('user_id', $this->userId)
////                ->first();
//            $shop = Shop::firstByUserId($this->userId);
//        } else {
//            $shop = Shop::firstById($this->shop->id);
//        }
        $shop = $this->shop;
        if (empty($shop)) {
            throw new ApiException(ErrorConst::SHOP_NOT_FOUND);
        }
        return $shop;
    }


    /**
     * 授权后的同步订单
     * @return bool
     */
    public function syncOrderByAuth()
    {
        $auth = $this->getShop();
        $beforeDay = strtotime("-7 day");
        $beginTime = $auth->last_sync_at ?: date("Y-m-d H:i:s", $beforeDay);
        \Log::info('授权以后同步订单', [$auth, $beginTime]);
        if (strtotime($beginTime) < $beforeDay) {
            $beginTime = date("Y-m-d H:i:s", $beforeDay);
        }
        $endTime = date("Y-m-d H:i:s");
        $redis = redis('cache');

        $redisKey = 'sync_order_run:' . $auth->id;
        $bool = $redis->exists($redisKey);
        if ($bool) {
            return false;
        }
        dispatch((new SyncOrdersJob(
            $auth,
            $beginTime,
            $endTime
        )));
        $redis->setex($redisKey, 1 * 60, time());
        return true;
    }


    /**
     * 翻页
     * <AUTHOR>
     */
    public function pageTurning()
    {
//        $this->hasNext = false;
        $this->page++;
    }

    /**
     * 保存服务订购
     * @return bool
     * @throws ApiException
     * <AUTHOR>
     */
    public function saveUserEdition(): bool
    {
        try {
            $data = $this->sendServiceInfo();

            Log::info('订购数据', [$data]);
        } catch (\Throwable $ex) {
            Log::info('获取订购出错', [$ex->getMessage(),$ex->getTraceAsString()]);
            return false;
        }
        if (empty($data)) {
            Log::error('获取订购数据出错', [$data]);
            return false;
        }
        if ($this->isDy() && in_array($data['version'], [UserExtra::VERSION_FREE, UserExtra::VERSION_STANDARD, UserExtra::VERSION_PROFESSIONAL])) {
            $platformOrder = PlatformOrder::getHighLevelByLevel($this->shop);
            if (!empty($platformOrder)) {
                $data['version_desc'] = $platformOrder['sku_spec'];
                $data['version'] = UserExtra::getVersionValueByName($data['version_desc']);
            }
        }

        // 用户版本
        $userEdition = UserExtra::query()->where([
            'user_id' => $data['user_id'],
            'shop_id' => $data['shop_id'],
        ])->first();
        if ($userEdition) {
            $userEdition->identifier = $data['identifier'];
            $userEdition->version = $data['version'];
            $userEdition->version_desc = $data['version_desc'];
            $userEdition->expire_at = $data['expired_at'];
            $userEdition->expire_days = Carbon::now()->diffInDays(Carbon::parse($data['expired_at']));

            if (!$userEdition->save()) {
                Log::error('userEdition info update Failed', ['user_id' => $data['user_id']]);
            }
        } else {
            $userEdition = UserExtra::query()->create([
                'user_id' => $data['user_id'],
                'shop_id' => $data['shop_id'],
                'identifier' => $data['identifier'],
                'buy_at' => date('Y-m-d H:i:s'),
                'expire_at' => $data['expired_at'],
                'expire_days' => Carbon::now()->diffInDays(Carbon::parse($data['expired_at'])),
                'version' => $data['version'], // 版本
                'version_desc' => $data['version_desc'], // 版本
                'pay_amount' => $data['pay_amount'] ?? 0, // 版本
            ]);
            if (!$userEdition) {
                Log::error('userEdition info create Failed', ['user_id' => $data['user_id']]);
            }
        }

        if (config('app.platform') == 'ks') {
            $shop = $this->getShop();
            PlatformOrder::query()->where('auth_user_id', $shop->auth_user_id)->update([
                'user_id' => $shop->user_id,
                'shop_id' => $shop->id,
                'identifier' => $shop->identifier,
            ]);
        }

        return true;
    }

    /**
     * 保存绑定店铺的服务订购
     * @return bool
     * @throws ApiException
     * <AUTHOR>
     */
    public function saveBindUserEdition(): bool
    {
        $shop = $this->getShop();
        $user = User::find($shop->user_id);
        //自己关联的店铺
        $user->shopList = array_merge(collect($user->shops())->toArray(), collect($user->inviteShops)->toArray());
        //父级的店铺+父级邀请的店铺
        if ($shop->inviteUser) {
            $user->shopList = array_merge($user->shopList, collect($shop->inviteUser->shops())->toArray(), collect($shop->inviteUser->inviteShops)->toArray());
        }
        //去重
        $user->shopList = collect($user->shopList)->unique()->values()->all();
        foreach ($user->shopList as $items) {
            if ($items['id'] == $shop['id']) {
                // 外面调用过了，这里就跳过
                continue;
            }
            $thatShop = (object)$items;
            Log::info('遍历的shops', [$thatShop]);
            dispatch(new SaveUserEdition($thatShop));
        }
        return true;
    }

    /**
     * 获取退款订单
     * @param int $startTime
     * @param int $endTime
     * @return array|mixed
     * <AUTHOR>
     */
    public function getRefundOrders(int $startTime, int $endTime)
    {
        $orders = $this->sendRefundOrders($startTime, $endTime);
        //错误情况处理
//	    if (isset($orders['code']) && in_array($orders['code'], $this->errorCodeArr)) {
//		    return $orders;
//	    }

        $orders = array_map(function ($order) {
            $order = $this->formatRefundOrder($order);
            return $order;
        }, $orders);
        return $orders;
    }

    /**
     * 请求售后列表接口
     * @param int $startTime
     * @param int $endTime
     * @return mixed
     * <AUTHOR>
     */
    abstract protected function sendRefundOrders(int $startTime, int $endTime);

    /**
     * 格式化退款订单
     * @param $order
     * @return mixed
     * <AUTHOR>
     */
    abstract protected function formatRefundOrder($order);

    /**
     * 转换退款状态
     * @param $status
     * @return mixed
     * <AUTHOR>
     */
    protected function formatRefundStatus($status)
    {
        if (empty($this->refundStatusMap)) {
            throw new InvalidArgumentException('未定义退款状态！');
        }
        if (!isset($this->refundStatusMap[$status])) {
            throw new InvalidArgumentException('未知的退款状态：' . $status);
        }
        return $this->refundStatusMap[$status];
    }

    /**
     * @return int|string
     */
    public function initPage()
    {
        $this->page = 1;
        $this->page_cursor = '';
    }


    /**
     * 机构拉取订单列表
     * @param $startTime
     * @param $endTime
     * @param $flag
     * @param $remark
     * @param $page
     * @param $pageSize
     * @param $orderStatus
     * @return array
     * <AUTHOR>
     * 22-2-14 去掉了userId
     */
    public function getOrderListForOpenApi($startTime, $endTime, $flag, $remark, $page, $pageSize, $orderStatus)
    {
        $shop = $this->getShop();
        $shopId = $shop->id;

        $conditions = [];
        if ($flag) {
            $conditions[] = ['seller_flag', $flag];
        }
        if (!empty($remark)) {
            $conditions[] = ['seller_memo', 'like', '%' . $remark . '%'];
        }
        $conditions[] = ['shop_id', $shopId];
        $conditions[] = ['order_created_at', '>=', date('Y-m-d', $startTime)];
        $conditions[] = ['order_created_at', '<=', date('Y-m-d', $endTime) . ' 23:59:59'];
        switch ($orderStatus) {
            case 1: //未打印
                $conditions[] = ['order_status', Order::ORDER_STATUS_PAYMENT];
                $conditions[] = ['print_status', Order::PRINT_STATUS_NO];
                $conditions[] = ['refund_status', Order::REFUND_STATUS_NO];
                break;
            case 2: //已打印未发货
                $conditions[] = ['order_status', Order::ORDER_STATUS_PAYMENT];
                $conditions[] = ['print_status', Order::PRINT_STATUS_YES];
                $conditions[] = ['refund_status', Order::REFUND_STATUS_NO];
                break;
            case 3: //已发货
                $conditions[] = ['order_status', Order::ORDER_STATUS_DELIVERED];
                $conditions[] = ['refund_status', Order::REFUND_STATUS_NO];
                break;
            default:
                break;
        }

        $count = Order::query()->where($conditions)->count('*');
        $data = [
            'page' => intval($page),
            'pageSize' => $pageSize,
            'totalPage' => 0,
            'totalSize' => 0,
            'orderList' => []
        ];
        if ($count > 0) {
            $offset = (intval($page) - 1) * $pageSize;
            $result = Order::query()->with('orderCipherInfo')->where($conditions)->offset($offset)->limit($pageSize)
                ->orderBy('order_updated_at', 'desc')->get();

            $data['totalSize'] = $count;
            $data['totalPage'] = ceil($count / $pageSize);
            foreach ($result as $item) {
                $data['orderList'][] = [
                    'receiverName' => in_array(config('app.platform'), [PlatformConst::DY, PlatformConst::JD, PlatformConst::KS])
                        ? (!empty($item['OrderCipherInfo']) ? $item['OrderCipherInfo']['receiver_name_mask'] : dataDesensitizationForOpenApi($item['receiver_name'], 1))
                        : dataDesensitizationForOpenApi($item['receiver_name'], 1), //收件人姓名脱敏处理
                    'receiverPhone' => in_array(config('app.platform'), [PlatformConst::DY, PlatformConst::JD, PlatformConst::KS])
                        ? (!empty($item['OrderCipherInfo']) ? $item['OrderCipherInfo']['receiver_phone_mask'] : dataDesensitizationForOpenApi($item['receiver_phone'], 3, 4))
                        : dataDesensitizationForOpenApi($item['receiver_phone'], 3, 4), //收件人手机脱敏处理
                    'province' => $item['receiver_state'],
                    'city' => $item['receiver_city'],
                    'district' => $item['receiver_district'],
                    'town' => $item['receiver_town'],
                    'address' => in_array(config('app.platform'), [PlatformConst::DY, PlatformConst::JD, PlatformConst::KS]) ?
                        (!empty($item['OrderCipherInfo']) ? $item['OrderCipherInfo']['receiver_address_mask'] : dataDesensitizationForOpenApi($item['receiver_address'], -4))
                        : dataDesensitizationForOpenApi(addressDesensitization($item['receiver_address']), -4),
                    'orderSn' => $item['tid'],
                    'goodsName' => $item['goods_title'],
                    'goodsSpec' => $item['sku_value'],
                    'goodsNum' => $item['num'],
                    'payAmount' => $item['payment'],
                    'isPreSale' => $item['is_pre_sale'],// 是否预售
                    'lastShipTime' => $item['promise_ship_at'],// 最晚发货时间
                    'confirmTime' => $item['groupon_at'], //成团时间
                    'remark' => implode(",", json_decode($item['seller_memo'], true)), // 卖家备注
                    'remarkTag' => $item['seller_flag'], // 卖家旗帜
                    'buyerMessage' => $item['buyer_message'], // 买家留言
                    'orderStatus' => $item['order_status'],
                    'createTime' => $item['order_created_at'],
                    'refundStatus' => $item['refund_status']
                ];
            }
        }

        return $data;
    }

    /**
     * <AUTHOR>
     * 22-2-14 去掉了userId
     */
    public function getOrdersInfoForOpenApi($orderSnsArr)
    {
        $shop = $this->getShop();
        $shopId = $shop->id;
        $conditions[] = ['shop_id', $shopId];
        $result = Order::query()->with('orderCipherInfo')->where($conditions)->whereIn('tid', $orderSnsArr)->get();
        $data = [];

        if (!empty($result)) {
            foreach ($result as $item) {
                $data[] = [
                    'receiverName' => in_array(config('app.platform'), [PlatformConst::DY, PlatformConst::JD, PlatformConst::KS])
                        ? (!empty($item['OrderCipherInfo']) ? $item['OrderCipherInfo']['receiver_name_mask'] : dataDesensitizationForOpenApi($item['receiver_name'], 1))
                        : dataDesensitizationForOpenApi($item['receiver_name'], 1), //收件人姓名脱敏处理
                    'receiverPhone' => in_array(config('app.platform'), [PlatformConst::DY, PlatformConst::JD, PlatformConst::KS])
                        ? (!empty($item['OrderCipherInfo']) ? $item['OrderCipherInfo']['receiver_phone_mask'] : dataDesensitizationForOpenApi($item['receiver_phone'], 3, 4))
                        : dataDesensitizationForOpenApi($item['receiver_phone'], 3, 4), //收件人手机脱敏处理
                    'province' => $item['receiver_state'],
                    'city' => $item['receiver_city'],
                    'district' => $item['receiver_district'],
                    'town' => $item['receiver_town'],
                    'address' => in_array(config('app.platform'), [PlatformConst::DY, PlatformConst::JD, PlatformConst::KS]) ? (!empty($item['OrderCipherInfo']) ? $item['OrderCipherInfo']['receiver_address_mask'] : dataDesensitizationForOpenApi($item['receiver_address'], -4)) : dataDesensitizationForOpenApi(addressDesensitization($item['receiver_address']), -4),
                    'orderSn' => $item['tid'],
                    'goodsName' => $item['goods_title'],
                    'goodsSpec' => $item['sku_value'],
                    'goodsNum' => $item['num'],
                    'payAmount' => $item['payment'],
                    'isPreSale' => $item['is_pre_sale'],// 是否预售
                    'lastShipTime' => $item['promise_ship_at'],// 最晚发货时间
                    'confirmTime' => $item['groupon_at'], //成团时间
                    'remark' => implode(",", json_decode($item['seller_memo'], true)), // 卖家备注
                    'remarkTag' => $item['seller_flag'], // 卖家旗帜
                    'orderStatus' => $item['order_status'],
                    'createTime' => $item['order_created_at'],
                    'refundStatus' => $item['refund_status'],
                ];
            }
        }

        return $data;
    }

    public function syncHistoryOrder($beginTime, $endTime)
    {
        $auth = $this->getShop();
        $redis = redis('cache');
        $redisKey = 'sync_history_order_run:' . $auth->user_id;
        $bool = $redis->exists($redisKey);
        if ($bool) {
            return false;
        }
        dispatch((new SyncHistoryOrdersJob(
            $auth,
            $beginTime,
            $endTime
        )));
        $redis->setex($redisKey, 1 * 60, time());
        return true;
    }

    public function delHistoryOrder($beginTime, $endTime)
    {
        $auth = $this->getShop();
        dispatch((new DeleteHistoryOrderJob(
            $auth,
            $beginTime,
            $endTime
        )));
        return true;
    }

    /**
     * 批量修改备注
     * @param $tidList
     * @param $sellerFlag
     * @param $sellerMemo
     * @return string[]
     * @throws ErrorCodeException
     */
    public function sendBatchEditSellerRemark($tidList, $sellerFlag, $sellerMemo):array
    {
        throwUnsupportedException("批量修改备注暂不支持");
        return [];
    }

    /**
     * @throws ErrorCodeException
     */
    public function batchEditSellerRemark($tidList, $sellerFlag, $sellerMemo): array
    {

        if($this->shop->isPdd()||$this->shop->isDy()||$this->shop->isJd()||$this->shop->isTaobao()||$this->shop->isKs()){
            return $this->sendBatchEditSellerRemark($tidList, $sellerFlag, $sellerMemo);
        }else {
            $failTids = [];
            foreach ($tidList as $index => $tid) {
                if ($index % 100 == 0) {
                    // 接口配置限流QPS:200
                    sleep(1);
                }
                try {
                    $result = $this->sendEditSellerRemark($tid, $sellerFlag, $sellerMemo);
                    if (!$result) {
                        $failTids[] = $tid;
                    }

                } catch (\Exception $ex) {
                    Log::info("修改备注失败" . $ex->getMessage());
                    $failTids [ $tid]=$ex->getMessage();
                }
            }
        }
        return $failTids;
    }


    /**
     * 修改备注
     * @param $tid
     * @param $sellerFlag
     * @param $sellerMemo
     * @return bool
     */
    abstract public function sendEditSellerRemark($tid, $sellerFlag, $sellerMemo): bool;


    abstract public function sendServiceOrderList($beginAt, $endAt);

    public function getRefundOrderInfo($afterSaleId)
    {
        $refundOrder = $this->sendRefundOrderInfo($afterSaleId);
        //错误情况处理
        if (isset($refundOrder['code']) && in_array($refundOrder['code'], $this->errorCodeArr)) {
            return $refundOrder;
        }

        $order = $this->formatRefundOrder($refundOrder);
        return $order;
    }

    /**
     * 售后订单列表
     * @param $tid
     * @return mixed
     */
    public function getRefundOrderList($tid)
    {
        $refundOrders = $this->sendRefundOrderList($tid);
        //错误情况处理
        if (isset($refundOrders['code']) && in_array($refundOrders['code'], $this->errorCodeArr)) {
            return $refundOrders;
        }

        $orders = $this->formatRefundOrder($refundOrders);
        return $orders;
    }


    /**
     * @throws ErrorCodeException
     */
    protected function sendBatchGetOrderInfo($orders, $safe = false): array
    {
        throwUnsupportedException("获取订单信息不支持");
        return [];
    }

    /**
     * @param array{
          *     tid:string,
          * }[] $orders
     * @param $safe
     * @return array
     * @throws ErrorCodeException
     */
    public function batchGetOrderInfo(array $orders, $safe = false): array
    {
        $list = $this->sendBatchGetOrderInfo($orders, $safe);
//        Log::info("tradesOrder=", [$list]);
        return $this->formatToOrders($list);
    }

    /**
     * @param array $params
     * @param string $method
     * @param null $timeout
     * @param bool $safe
     * @return array
     */
    public function poolCurl($params = [], $method = 'GET', $timeout = null, $safe = false)
    {
        $headers = array();
        $keyArr = array_keys($params);
        switch ($method) {
            case 'get':
                $postKey = 'query';
                break;
            case 'post':
                $postKey = 'json';
                $headers = [
                    'Content-type' => 'application/json',
                    "Accept" => "application/json"
                ];
                break;
            case 'post_form':
                $method = 'post';
                $postKey = 'form_params';
                break;
            default:
                $postKey = 'json';
                break;
        }
        if (isset($timeout)) {
            $client = new Client(['timeout' => $timeout]);
        } else {
            $client = new Client();
        }
//        if (config('app.platform') == PlatformConst::JD) {
//            $client = $this->getJdClient()->getHttpClient();
//        } else if (config('app.platform') == PlatformConst::TAOBAO) {
//            $client = $this->getTbClient()->getHttpClient();
//        } else {
//            $client = $this->getClient()->getHttpClient();
//        }
        $requests = function ($data) use ($client, $method, $headers, $postKey) {
            foreach ($data as $index => $datum) {
                yield function () use ($client, $method, $postKey, $headers, $datum) {
//                    Log::info('item', [$datum]);
                    return $client->requestAsync($method, $datum['url'], [
                        $postKey => $datum['params'],
                        'headers' => $headers
                    ]);
                };
            }
        };

        $result = [];
        $pool = new Pool($client, $requests($params), [
            'concurrency' => $this->concurrency, //并发数
            'fulfilled' => function (Response $response, $index) use (&$result, $params, $keyArr) {

                $orderIdStr = $keyArr[$index];
                $res = [
                    'http_code' => $response->getStatusCode(), // 200
                    'reason' => $response->getReasonPhrase(), // OK
                    //'data' => $response->getBody()->getContents()
                    'data' => $response->getBody()
                ];

                $content = $this->handleResponse($res, true);
                $result[$orderIdStr] = [
                    'sort' => $index,
                    'orderIdStr' => $orderIdStr,
                    'content' => $content,
                ];
                return $result;
            },
            'rejected' => function (\Exception $e, $index) use ($safe, $keyArr) {
                $orderIdStr = $keyArr[$index];
                if ($safe) {
                    //遇到安全模式，只记录log啥也不干
                    Log::warning("遇到异常了，记录一下", ["orderIdStr" => $orderIdStr, "message" => $e->getMessage()]);
                } else {
                    throw new \Exception($e->getMessage());
                }

            },
        ]);
        // 初始化并创建promise
        $promise = $pool->promise();
        // 等待所有进程完成
        $promise->wait();

        array_multisort(array_column($result, 'sort'), SORT_ASC, $result);
        //$result = Arr::pluck($result, 'content', 'sort');
        $result = Arr::pluck($result, 'content', 'orderIdStr');
        //Log::info('result', [$result]);

        return $result;
    }

    /**
     * 数据格式转换
     * @param array $response
     * @param bool $isPoolCurl
     * @return bool|mixed|null
     * @throws \Exception
     */
    public function handleResponse(array $response, bool $isPoolCurl = false)
    {
        if ($response['http_code'] != 200) {
            return false;
        }
        $result = null;
        if ($this->dataType == 'JSON') {
            $response_ = preg_replace('/id":(\\d{11,})/', 'id":"\\1"', $response['data']);
            $respObject = json_decode($response_);
            //记录错误日志
            if (isset($respObject->err_no) && $respObject->err_no != 0) {
                Log::error('Order api请求错误====', [$response]);
            }
            $result = $respObject;

            //抛出异常
            if ((isset($result->sub_code) && !empty($result->sub_code)) && (isset($result->sub_msg) && !empty($result->sub_msg))) {
                //异步池请求，防止进程退出
                if ($isPoolCurl) {
                    if ($this->poolCurlAbnormalOutputOriginalData) {
                        return json_decode(json_encode($result), true);
                    } else {
                        return $result->sub_msg;
                    }
                } else {
                    throw new \Exception($result->sub_msg);
                }
            }
            return $result;
        }

        return $response['data'];
    }

    /**
     * 批量解密
     * @param $list [tid,receiver_phone,receiver_name,receiver_address]
     * @return mixed
     */
    public function batchDecrypt($list)
    {
        $result = [];
        foreach ($list as $key => $item) {
            if ($key == 'tid') {
                continue;
            }
            $data = [
                'tid' => $list['tid'],
                'text' => $item,
                'filed' => $key
            ];
            $res = $this->sendDecrypt($data);

            $res0 = $res[0];
            $result[$key] = ArrayUtil::getArrayValue($res0, 'text');
        }
        \Log::info("sendDecrypt rsult", $result);
        return $result;
    }

    /**
     * 返回加密数据
     * @param $param
     * @return array{receiverPhone:string ,receiverName:string,receiverAddress:string}
     * @throws ErrorCodeException
     */
    public function getEncryptData($param): array
    {
        throw_error_code_exception(ErrorCode::NOT_IMPLEMENTED, 'getEncryptData');
    }

    public function batchEncrypt($list)
    {
        return $this->sendBatchEncrypt($list);
    }

    /**
     * 获取代打店铺角色类型
     * <AUTHOR>
     */
    abstract public function sendFactoryShopRoleType(): int;

    /**
     * 获取代打订单列表
     * @param int $startTime
     * @param int $endTime
     * @return mixed
     * @throws OrderException|ClientException|ApiException
     * <AUTHOR>
     */
    abstract public function getFactoryTradesOrder(int $startTime, int $endTime);

    /**
     * @param int $i
     * @param int $orderTimeInterval
     * @param $beginAt
     * @param $endAt
     * @return array
     * <AUTHOR>
     */
    public function calcTimeInterval(int $i, int $orderTimeInterval, $beginAt, $endAt): array
    {
        $addMinutes = $i * $orderTimeInterval;
        $startAt = date('Y-m-d H:i:s', strtotime("+$addMinutes minute", strtotime($beginAt)));

        $startTime = strtotime($startAt);
        $endTime = strtotime("+$orderTimeInterval minute", $startTime);
        //时间超出当前时间
        if ($endTime > strtotime($endAt)) {
            $endTime = strtotime($endAt);
        }
        return array($startTime, $endTime);
    }

    /**
     * 批量获取厂家订单信息
     * @param $orders
     * @return mixed
     * <AUTHOR>
     */
    abstract public function batchGetFactoryOrderInfo($orders);

    /**
     * 批量电子面单回传并发货
     * @param $orders
     * @return OrderResponseBo[]
     * <AUTHOR>
     */
    abstract public function batchReturnFactoryOrder($orders);

    abstract public function batchWaybillRecoveryFactoryOrder($waybills);

    abstract public function sendGetSellerList();

    public function getSellerList()
    {
        $shop = $this->getShop();
        if ($shop->role_type != Shop::ROLE_TYPE_FACTORY) {
            return [];
        }
        do {
            $sellerList = $this->sendGetSellerList();
            if (!empty($sellerList)) {
                //绑定关系&&插入代打商家
                foreach ($sellerList as $item) {
                    $shopInfo = Shop::query()->where(['name' => $item['seller_shop_name'],
                        'shop_name' => $item['seller_shop_name'],
                        'identifier' => $item['seller_shop_id'],
                        'role_type' => ShopBind::TYPE_AGENT_PRINT_FACTORY_SHOP])->first();
                    if (empty($shopInfo)) {
                        //插入店铺
                        $shopInfo = Shop::create([
                            'name' => $item['seller_shop_name'],
                            'shop_name' => $item['seller_shop_name'],
                            'identifier' => $item['seller_shop_id'],
                            'role_type' => ShopBind::TYPE_AGENT_PRINT_FACTORY_SHOP,
                        ]);
                        if ($shopInfo) {
                            //插入绑定关系
                            ShopBind::create([
                                'f_shop_id' => $shop->id,
                                'o_shop_id' => $shopInfo->id,
                                'type' => 5
                            ]);
                        } else {
                            \Log::error('插入平台代打商家失败:  厂商：' . json_encode($shop) . '  商家:' . json_encode($item));
                        }
                    }
                }
            }
            //翻页
            $this->pageTurning();

        } while ($this->hasNext);

        return true;
    }


    /**
     * 请求查询交易订单的 Tid
     * @param array $query_list
     * <AUTHOR>
     */
    abstract public function sendQueryTradeTid(array $query_list);

    /**
     * 日志批量上传
     * @param Event $event
     * @return bool
     */
    public function reportBatchLogByEvent(Event $event): bool
    {
        // 请在继承的类添加日志上传逻辑
        return true;
    }

    /**
     * 请求地址列表
     * @return array [{"name":"海南省","code":460000,"parent_code":1,"level":1,"sub":[{"name":"海口市","code":460100,"parent_code":460000,"level":2,"sub":[{"name":"其它区","code":460109,"parent_code":460100,"level":3}]}]}]
     * <AUTHOR>
     */
    abstract public function sendAddress(): array;


    protected function getDistrictCodeByAddress($receiver_state, $receiver_city, $receiver_district)
    {
        return Address::getDistrictCodeAndOther($receiver_state, $receiver_city, $receiver_district);
    }

    /**
     * 关键字查找订单
     * @param string $type 取值 receiver_phone 手机号，receiver_name 姓名
     * @param string $search
     * @return int[]
     */

    abstract public function getQueryTradeOrderId(string $type, string $search):array;

    /**
     * 通过订单填充 api参数
     * @param $apiMethod
     * @param $apiParams
     * @param  $order
     * @param null $orderShop
     * @return array
     */
    abstract public function fillApiParamByOrder($apiMethod, $apiParams, $order, $orderShop = null): array;

    /**
     * 通过订单填充 api参数
     * @param $requestMethod
     * @param $apiMethod
     * @param $apiParams
     * @param  $order
     * @return array
     */
    abstract public function sendByCustom($requestMethod, $apiMethod, $apiParams, $order);


    public function pushPlatformOrder()
    {
        try {
            if (config('app.platform') == PlatformConst::TAOBAO) {
                $shop = $this->getShop();
                // 判断是否是礼品网用户
                $apiShopBind = ApiShopBind::query()->where('shop_id', $shop->id)->first();
                if (!empty($apiShopBind)) {
                    //推送订购数据
                    $platformOrder = PlatformOrder::query()->where(['shop_id' => $shop->id, 'is_push' => 0])->whereNotNull('order_id')->get();
                    if ($platformOrder->isNotEmpty()) {
                        foreach ($platformOrder as $item) {
                            $pushData[] = [
                                'mallName' => $shop->name,
                                'amount' => $item->pay_fee / 100,
                                'orderTime' => $item->order_created_at,
                                'orderNo' => $item->order_id,
                                'days' => $item->duration,
                                'orderStatus' => 1,
                                'appkey' => $apiShopBind->app_id,
                                'shopId' => $shop->id,
                                'skuId' => $item->sku_id,
                                'skuName' => $item['sku_spec'],
                                'serviceName' => $item['service_name'],
                                'agentCode' => $apiShopBind->agent_code ?? '',
                                'shopName' => $shop->shop_name ?? '',
                            ];
                        }
                        $formParams = [
                            'platformCode' => PlatformConst::TAOBAO,
                            'traceId' => session_create_id(),
                            'timestamp' => time(),
                        ];
                        //生成签名
                        $data['list'] = $pushData;
                        $formParams['sign'] = $formParams['sign'] = GenerateSignForCommission($formParams, $data);
                        //push
                        $client = new Client([
                            'timeout' => 3,
                            'connect_timeout' => 3,
                        ]);
                        $response = $client->request('POST', 'http://180.184.68.244:8082/api/fuwu/order/sync?' . http_build_query($formParams), [
                            'json' => $data,
                        ]);

                        $body = json_decode($response->getBody(), true);
                        \Log::info('推送平台订单到结算系统 params:' . json_encode(array_merge($formParams, $data)) . ' response:' . json_encode($body));
                        if (!empty($body)) {
                            if (isset($body['data']) && $body['data'] == 'SUCCESS') {
                                //push成功更改状态
                                PlatformOrder::query()->whereIn('id', $platformOrder->pluck('id'))->update(['is_push' => 1]);
                            }
                        }
                    }
                }
            }
        } catch (\Exception $exception) {
            \Log::error('推送订单失败: ' . $exception->getMessage());
        }

        return true;
    }

    /**
     * 检查订单是否要质检
     * @return CommonResponse[]
     * <AUTHOR>
     */
    public function checkOrderQualityInspection($orders)
    {
        return [];
    }

    /**
     * 获取质检结果
     * @return CommonResponse[]
     */
    public function getQualityInspectionResult($orders)
    {
        return [];
    }

    /**
     * 质检订单发货
     * @param OrderDeliveryRequest[] $orderDeliveryRequests
     * @return CommonResponse[]
     */
    public function deliveryQualityInspection(array $orderDeliveryRequests)
    {
        return [];
    }

    /**
     * @param ReportOrderSecurityEventRequest[] $list
     * <AUTHOR>
     */
    public function reportOrderSecurityEvent(array $list)
    {
        throw new \Exception('未实现：' . __FUNCTION__);
        return null;
    }

    /**
     * 获取备注
     * @return mixed
     */
    public function sendGetRemarks(string $startTime, string $endTime, int $page, int $sortType = 1)
    {
        throw_error_code_exception(StatusCode::ILLEGAL_OPERATION);

    }

    public function getRemarks(string $startTime, string $endTime, int $page, int $sortType = 1)
    {
        $remarkInfo = $this->sendGetRemarks($startTime, $endTime, $page, $sortType);
        $remarks = [];
        foreach ($remarkInfo['remarkList'] ?? [] as $remark) {
            $remarks[] = $this->formatRemark($remark);
        }
        return ["page" => $page, "remarks" => $remarks, "totalNum" => $remarkInfo['totleNum'], "pageSize" => 100];

    }

    public function formatRemark($remark)
    {
        throw_error_code_exception(StatusCode::ILLEGAL_OPERATION);
    }

    /**
     * 判断订单号是否能合单
     * @param Order[] $orderList
     * @return array
     * @throws ErrorCodeException
     */
    public function sendCheckMergeOrder(array $orderList): array
    {
        throw_error_code_exception(StatusCode::ILLEGAL_OPERATION);
    }

    protected function flagErrorMsg(string $sellerFlag): string
    {
        return $sellerFlag . '旗标不支持';
    }

    public function isJd(): bool
    {
        return $this->platformType==PlatformConst::PLATFORM_TYPE_JD;
    }

    public function isTaobao(): bool
    {
        return $this->platformType==PlatformConst::PLATFORM_TYPE_TAOBAO;
    }
    public function isPdd(): bool
    {
        return $this->platformType==PlatformConst::PLATFORM_TYPE_PDD;
    }

    public function isKs(): bool
    {
        return $this->platformType==PlatformConst::PLATFORM_TYPE_KS;
    }

    public function isDy(): bool
    {
        return $this->platformType==PlatformConst::PLATFORM_TYPE_DY;
    }
    /**
     * @param array $skuList
     * @return array|string[]
     */
    protected function getSkuValueAnd12(array $skuList)
    {
        $skuValue = '默认;';
        $skuValue1 = '';
        $skuValue2 = '';
        if (empty($skuList)) {
            return [$skuValue, $skuValue1, $skuValue2];
        }
        $skuValue = implode(';', array_pluck($skuList,'value'));
        // 循环匹配，如果匹配到颜色那就优先赋值给 $skuValue1，如果匹配到尺寸那就优先赋值给 $skuValue2
        foreach ($skuList as $index => $item) {
            if ($index > 1) { // 第三层开始不处理
                break;
            }
            if (preg_match('/颜色/u', $item['name']) && empty($skuValue1)) {
                $skuValue1 = $item['value'];
            }
            if (preg_match('/(身高|尺码|尺寸)/u', $item['name']) && empty($skuValue2)) {
                $skuValue2 = $item['value'];
            }
        }
        // 循环结束，没赋值的
        if (empty($skuValue1)) {
            // 如果第一个没有赋值给 value2，那么就取第一个给 value1
            if ($skuList[0]['value'] != $skuValue2){
                $skuValue1 = $skuList[0]['value']??'';
            }else{
                $skuValue1 = $skuList[1]['value']??'';
            }
        }
        if (empty($skuValue2) && !empty($skuList[1])) {
            // 如果第二个没有赋值给 value1，那么就取第二个给 value2
            if ($skuList[1]['value'] != $skuValue1){
                $skuValue2 = $skuList[1]['value']??'';
            }else{
                $skuValue2 = $skuList[0]['value']??'';
            }
        }
        return [$skuValue, $skuValue1, $skuValue2];
    }
}
