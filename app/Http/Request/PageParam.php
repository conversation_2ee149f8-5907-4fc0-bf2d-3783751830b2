<?php

namespace App\Http\Request;

/**
 * 查询参数
 */
class PageParam
{
    public $page;
    public $pageSize;
    public $column= ['*'];
    public $pageName='page';
    /**
     * 排序，两维数组，第一维是列名，第二维是排序方向 asc|desc
     * @var array
     */
    public $sorts=[];

    public function addSort($column, $direction='asc'){
        $this->sorts[] = [$column,$direction];
    }

    public function getSorts($default=[]){
        if(!empty($this->sorts)){
            return $this->sorts;
        }
        return $default;
    }

    public function offset(){
        return ($this->page - 1) * $this->pageSize;
    }

}
