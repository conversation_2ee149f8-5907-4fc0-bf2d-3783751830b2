<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/3/17
 * Time: 15:54
 */

namespace App\Constants;

use App\Exceptions\ErrorCodeException;
use App\Models\Waybill;

class PlatformConst
{
    //店铺
    const TAOBAO = 'taobao_top';
    const PDD = 'pdd';
    const KS = 'ks';
    const JD = 'jd';
    const WX = 'wx';
    const DY = 'dy';
    const WXSP = 'wxsp';
    const XHS = 'xhs';
    const ALBB = 'albb';
    const ALC2M = 'alc2m';
    const CN = 'cn';
    const OTHER = 'other';
    const UNI = 'uni';

    const PLATFORM_NAME = [
        self::TAOBAO => '淘宝',
        self::PDD => '拼多多',
        self::KS => '快手',
        self::JD => '京东',
        self::WX => '微信',
        self::DY => '抖音',
        self::WXSP => '微信视频号',
        self::XHS => '小红书',
        self::ALBB => '阿里巴巴',
        self::ALC2M => '淘工厂',
        self::CN => '菜鸟',
        self::UNI => '统一平台',
    ];
    const ALL_PLATFORM = [
        self::TAOBAO,
        self::PDD,
        self::KS,
        self::JD,
        self::WX,
        self::DY,
        self::WXSP,
    ];

    const WAYBILL_MAPPING = [
        self::TAOBAO => self::WAYBILL_TB_TOP,
        self::PDD => self::WAYBILL_PDD,
        self::KS => self::WAYBILL_KS,
        self::JD => self::WAYBILL_JD,
        self::WXSP => self::WAYBILL_WXSP,
        self::DY => self::WAYBILL_DY,
        self::XHS => self::WAYBILL_XHS
    ];
    // 支持追加的平台
    const SUPPORT_APPEND_PLATFORM = [
        self::DY,
        self::KS,
        self::TAOBAO,
    ];

    //电子面单
    const PDD_WB = 'pddwb';   //pdd站外面单
    const TWC = 'twc';    //第三方淘宝面单
    const NEW_TWC = 'newtwc'; //自己的淘宝服务
    const LINK = 'link';   //菜鸟Link
    const TB = 'taobao'; //淘宝

    const WAYBILL_PDD_WB = 1; //pdd wb
    const WAYBILL_TB = 2; //淘宝站外
    const WAYBILL_CNLINK = 3; //菜鸟Link
    const WAYBILL_PDD = 4; //pdd 站内
    const WAYBILL_TB_TOP = 5; //淘宝站内
    const WAYBILL_DY = 6; //抖音
    const WAYBILL_JD = 7; //京东
    const WAYBILL_KS = 8; //快手

    const WAYBILL_WXSP = 9; //微信视频号
    const WAYBILL_XHS = 10; //小红书


    const WAYBILL_AUTH_MAP = [
        self::WAYBILL_PDD => "拼多多",
        self::WAYBILL_PDD_WB => "拼多多站外面单",
        self::WAYBILL_TB => "淘宝站外",
        self::WAYBILL_CNLINK => "菜鸟",
        self::WAYBILL_JD => "京东",
        self::WAYBILL_KS => "快手",
        self::WAYBILL_WXSP => "微信视频号",
        self::WAYBILL_XHS => "小红书",
        self::WAYBILL_DY => "抖音",
        self::WAYBILL_TB_TOP => "淘宝",
    ];

    const APP_ID_TB_XXXXX = 'xxxxx'; //淘宝app_id


    public const PLATFORM_TYPE_OTHER = 0;
    public const PLATFORM_TYPE_USER = 99; // 用户的虚拟店铺
    public const PLATFORM_TYPE_TAOBAO = 1;
    public const PLATFORM_TYPE_KS = 3;
    public const PLATFORM_TYPE_CN = 11;
    public const PLATFORM_TYPE_JD = 4;
    public const PLATFORM_TYPE_ALBB = 9;
    public const PLATFORM_TYPE_DY = 5;
    public const PLATFORM_TYPE_WX = 6;
    public const PLATFORM_TYPE_WXSP = 7;
    public const PLATFORM_TYPE_PDD = 2;
    public const PLATFORM_TYPE_ALC2M = 10;
    public const PLATFORM_TYPE_XHS = 8;

    /**
     * 平台类型映射，数字->字符串
     */
    public const PLATFORM_TYPE_MAP_REVERT = [
        PlatformConst::PLATFORM_TYPE_TAOBAO => PlatformConst::TAOBAO,
        PlatformConst::PLATFORM_TYPE_PDD => PlatformConst::PDD,
        PlatformConst::PLATFORM_TYPE_KS => PlatformConst::KS,
        PlatformConst::PLATFORM_TYPE_JD => PlatformConst::JD,
        PlatformConst::PLATFORM_TYPE_WX => PlatformConst::WX,
        PlatformConst::PLATFORM_TYPE_DY => PlatformConst::DY,
        PlatformConst::PLATFORM_TYPE_WXSP => PlatformConst::WXSP,
        PlatformConst::PLATFORM_TYPE_XHS => PlatformConst::XHS,
        PlatformConst::PLATFORM_TYPE_ALBB => PlatformConst::ALBB,
        PlatformConst::PLATFORM_TYPE_ALC2M => PlatformConst::ALC2M,
        PlatformConst::PLATFORM_TYPE_CN => PlatformConst::CN,
        PlatformConst::PLATFORM_TYPE_USER => PlatformConst::UNI,
    ];

    /**
     * 平台类型映射，字符串->数字
     */
    public const PLATFORM_TYPE_MAP = [
        PlatformConst::TAOBAO => PlatformConst::PLATFORM_TYPE_TAOBAO,
        PlatformConst::PDD => PlatformConst::PLATFORM_TYPE_PDD,
        PlatformConst::KS => PlatformConst::PLATFORM_TYPE_KS,
        PlatformConst::JD => PlatformConst::PLATFORM_TYPE_JD,
        PlatformConst::DY => PlatformConst::PLATFORM_TYPE_DY,
        PlatformConst::WX => PlatformConst::PLATFORM_TYPE_WX,
        PlatformConst::WXSP => PlatformConst::PLATFORM_TYPE_WXSP,
        PlatformConst::XHS => PlatformConst::PLATFORM_TYPE_XHS,
        PlatformConst::ALBB => PlatformConst::PLATFORM_TYPE_ALBB,
        PlatformConst::ALC2M => PlatformConst::PLATFORM_TYPE_ALC2M,
        PlatformConst::CN => PlatformConst::PLATFORM_TYPE_CN,
    ];

    /**
     * 生意网平台类型映射
     */
    const SYW_TYPE_MAP = [
        'JD' => self::PLATFORM_TYPE_JD,
        'TAOBAO' => self::PLATFORM_TYPE_TAOBAO,
        'PDD' => self::PLATFORM_TYPE_PDD,
        'TIKTOK' => self::PLATFORM_TYPE_DY,
        'WEIDIAN' => self::PLATFORM_TYPE_WXSP,
        'REDBOOK' => self::PLATFORM_TYPE_XHS,
        'KUAISHOU' => self::PLATFORM_TYPE_KS,
        'YLBB' => self::PLATFORM_TYPE_ALBB,
        'OTHER' => self::PLATFORM_TYPE_OTHER,
    ];

    /**
     * 电子面单授权来源映射
     */
    const PLATFORM_TYPE_TO_AUTH_SOURCE_MAP = [
        PlatformConst::PLATFORM_TYPE_TAOBAO => Waybill::AUTH_SOURCE_TAOBAO,
        PlatformConst::PLATFORM_TYPE_JD => Waybill::AUTH_SOURCE_JD,
        PlatformConst::PLATFORM_TYPE_DY => Waybill::AUTH_SOURCE_DY,
        PlatformConst::PLATFORM_TYPE_KS => Waybill::AUTH_SOURCE_KS,
        PlatformConst::PLATFORM_TYPE_WXSP => Waybill::AUTH_SOURCE_WXSP,
        PlatformConst::PLATFORM_TYPE_XHS => Waybill::AUTH_SOURCE_XHS,
        PlatformConst::PLATFORM_TYPE_PDD => Waybill::AUTH_SOURCE_PDD,
        PlatformConst::PLATFORM_TYPE_CN => Waybill::AUTH_SOURCE_LINK,
        PlatformConst::PLATFORM_TYPE_ALBB => Waybill::AUTH_SOURCE_LINK,
        PlatformConst::PLATFORM_TYPE_ALC2M => Waybill::AUTH_SOURCE_LINK,
    ];

    /**
     * 把平台类型映射为平台名称
     * @param int $platformType
     * @return string
     *
     * @throws ErrorCodeException
     */
    public static  function map2Platform(int $platformType):string{
        $result= self::PLATFORM_TYPE_MAP_REVERT[$platformType] ?? null;
        if(!$result){
            throw_error_code_exception(ErrorConst::PARAM_ERROR,null,'平台类型错误');
        }
        return $result;
    }


    /**
     * 把平台名称映射为平台类型
     * @param string $platform
     * @return int
     * @throws ErrorCodeException
     */
    public static function map2PlatformType(string $platform):int
    {
        $result=self::PLATFORM_TYPE_MAP[$platform]??null;
        if(!$result){
            throw_error_code_exception(ErrorConst::PARAM_ERROR,null,'平台类型错误');
        }
        return $result;
    }

    public static function getPlatformName(string $platform): string
    {
        return PlatformConst::PLATFORM_NAME[$platform];
    }

    /**
     * 判断是否是京东
     * @param  int  $platformType
     * @return bool
     */
    public static function isJdByPlatformType(int $platformType):bool
    {
        return $platformType == PlatformConst::PLATFORM_TYPE_JD;
    }

    /**
     * 判断是否是快手
     * @param  int  $platformType
     * @return bool
     */
    public static function isKsByPlatformType(int $platformType):bool
    {
        return $platformType == PlatformConst::PLATFORM_TYPE_KS;
    }

    public static function isDyByPlatformType(int $platformType):bool
    {
        return $platformType == PlatformConst::PLATFORM_TYPE_DY;
    }

    public static function mapPlatformType2AuthSource(int $platformType){
        return self::PLATFORM_TYPE_TO_AUTH_SOURCE_MAP[$platformType]??null;
    }
}
