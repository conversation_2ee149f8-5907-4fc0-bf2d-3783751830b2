<?php

use GuzzleHttp\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Request;
use App\Services\Order\AbstractOrderService;

/**
 * poolCurl 方法自动重试功能集成测试
 * 使用 Mock Handler 模拟各种网络场景
 */
class PoolCurlRetryIntegrationTest extends TestCase
{
    /**
     * 创建可以注入 Mock Client 的测试服务
     */
    private function createTestOrderServiceWithMockClient($mockHandler)
    {
        return new class($mockHandler) extends AbstractOrderService {
            private $mockHandler;

            public function __construct($mockHandler) {
                $this->mockHandler = $mockHandler;
                parent::__construct();
            }

            // 重写 poolCurl 方法以使用 Mock Client
            public function poolCurl($params = [], $method = 'GET', $timeout = null, $safe = false, $autoRetry = false)
            {
                $headers = array();
                $keyArr = array_keys($params);
                switch ($method) {
                    case 'get':
                        $postKey = 'query';
                        break;
                    case 'post':
                        $postKey = 'json';
                        $headers = [
                            'Content-type' => 'application/json',
                            "Accept" => "application/json"
                        ];
                        break;
                    case 'post_form':
                        $method = 'post';
                        $postKey = 'form_params';
                        break;
                    default:
                        $postKey = 'json';
                        break;
                }

                // 创建客户端配置
                $clientConfig = [];
                if (isset($timeout)) {
                    $clientConfig['timeout'] = $timeout;
                }

                // 使用 Mock Handler
                $handlerStack = HandlerStack::create($this->mockHandler);

                // 如果开启自动重试，添加重试中间件
                if ($autoRetry) {
                    $handlerStack->push(\GuzzleHttp\Middleware::retry($this->getRetryDecider(), $this->getRetryDelay()));
                }

                $clientConfig['handler'] = $handlerStack;
                $client = new Client($clientConfig);

                $requests = function ($data) use ($client, $method, $headers, $postKey) {
                    foreach ($data as $index => $datum) {
                        yield function () use ($client, $method, $postKey, $headers, $datum) {
                            return $client->requestAsync($method, $datum['url'], [
                                $postKey => $datum['params'],
                                'headers' => $headers
                            ]);
                        };
                    }
                };

                $result = [];
                $pool = new \GuzzleHttp\Pool($client, $requests($params), [
                    'concurrency' => $this->concurrency,
                    'fulfilled' => function (Response $response, $index) use (&$result, $params, $keyArr) {
                        $orderIdStr = $keyArr[$index];
                        $res = [
                            'http_code' => $response->getStatusCode(),
                            'reason' => $response->getReasonPhrase(),
                            'data' => $response->getBody()
                        ];

                        $content = $this->handleResponse($res, true);
                        $result[$orderIdStr] = [
                            'sort' => $index,
                            'orderIdStr' => $orderIdStr,
                            'content' => $content,
                        ];
                        return $result;
                    },
                    'rejected' => function (\Exception $e, $index) use ($safe, $keyArr) {
                        $orderIdStr = $keyArr[$index];
                        if ($safe) {
                            \Log::warning("遇到异常了，记录一下", ["orderIdStr" => $orderIdStr, "message" => $e->getMessage()]);
                        } else {
                            throw new \Exception($e->getMessage());
                        }
                    },
                ]);

                $promise = $pool->promise();
                $promise->wait();

                array_multisort(array_column($result, 'sort'), SORT_ASC, $result);
                $result = \Illuminate\Support\Arr::pluck($result, 'content', 'orderIdStr');

                return $result;
            }

            protected $platformType = 'test';

            // 实现所有抽象方法（简单实现）
            public function formatToOrder(array $trade): array { return []; }
            public function formatToAfterSale(array $trade) { return []; }
            public function formatToOrders(array $orders): array { return []; }
            public function formatToGoods(array $goods): array { return []; }
            protected function sendGetTradesOrders(int $startTime, int $endTime, bool $isFirstPull = false) { return []; }
            protected function sendGetOrderInfo(string $tid) { return []; }
            protected function sendGetAfterSaleOrderInfo(string $tid) { return []; }
            protected function sendGetGoods(int $pageSize, int $currentPage) { return []; }
            protected function deliverySellerOrders(string $tid, string $expressCode, string $expressNo, array $orderItemOId, bool $silent = true) { return []; }
            protected function deliverySellerOrdersForOpenApi(string $tid, string $expressCode, string $expressNo, array $orderItemOId) { return []; }
            protected function sendGetTradesOrdersByIncr(int $startTime, int $endTime, bool $isFirstPull = false): array { return []; }
            public function sendServiceInfo(): array { return []; }
            protected function sendDecrypt($order) { return []; }
            protected function sendBatchEncrypt($order) { return []; }
            public function checkAuthStatus(): bool { return true; }
            protected function getClient() { return null; }
            public function openSubscribeMsg(): bool { return true; }
            public function consumeSubscribeMsg() { return []; }
            public function confirmSubscribeMsg(array $idArr) { return []; }
            public function handleSubscribeMsg($data) { return []; }
            public function createWebsocketConnection() { return null; }
            public function batchGetRefundApplyList($data) { return []; }
            protected function sendRefundOrders(int $startTime, int $endTime) { return []; }
            protected function formatRefundOrder($order) { return []; }
            public function sendEditSellerRemark($tid, $sellerFlag, $sellerMemo): bool { return true; }
            public function sendServiceOrderList($beginAt, $endAt) { return []; }
            protected function sendRefundOrderInfo($afterSaleId) { return []; }
            protected function sendRefundOrderList($tid) { return []; }
            public function batchDeliveryOrders(array $orderDeliveryRequests): array { return []; }
            public function sendFactoryShopRoleType(): int { return 1; }
            public function getFactoryTradesOrder(int $startTime, int $endTime) { return []; }
            public function batchGetFactoryOrderInfo($orders) { return []; }
            public function batchReturnFactoryOrder($orders) { return []; }
            public function batchWaybillRecoveryFactoryOrder($waybills) { return []; }
            public function sendGetSellerList() { return []; }
            public function sendQueryTradeTid(array $query_list) { return []; }
            public function sendAddress(): array { return []; }
            public function getQueryTradeOrderId(string $type, string $search): array { return []; }
            public function fillApiParamByOrder($apiMethod, $apiParams, $order, $orderShop = null): array { return []; }
            public function sendByCustom($requestMethod, $apiMethod, $apiParams, $order) { return []; }
        };
    }

    /**
     * 测试连接异常的重试场景
     */
    public function testRetryOnConnectException()
    {
        // 模拟：前2次连接失败，第3次成功
        $mock = new MockHandler([
            new ConnectException('Connection failed', new Request('GET', 'test')),
            new ConnectException('Connection failed', new Request('GET', 'test')),
            new Response(200, [], '{"success": true}')
        ]);

        $service = $this->createTestOrderServiceWithMockClient($mock);

        $params = [
            'test1' => [
                'url' => 'http://test.com/api',
                'params' => ['test' => 'data1']
            ]
        ];

        // 开启重试
        $result = $service->poolCurl($params, 'GET', 5, true, true);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('test1', $result);
        // 验证最终成功获得响应
        $this->assertNotNull($result['test1']);
    }

    /**
     * 测试服务器错误的重试场景
     */
    public function testRetryOnServerError()
    {
        // 模拟：前2次服务器错误，第3次成功
        $mock = new MockHandler([
            new Response(500, [], 'Internal Server Error'),
            new Response(502, [], 'Bad Gateway'),
            new Response(200, [], '{"success": true}')
        ]);

        $service = $this->createTestOrderServiceWithMockClient($mock);

        $params = [
            'test1' => [
                'url' => 'http://test.com/api',
                'params' => ['test' => 'data1']
            ]
        ];

        // 开启重试
        $result = $service->poolCurl($params, 'GET', 5, true, true);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('test1', $result);
    }

    /**
     * 测试超过最大重试次数的场景
     */
    public function testMaxRetriesExceeded()
    {
        // 模拟：连续4次都失败（超过最大重试次数3次）
        $mock = new MockHandler([
            new Response(500, [], 'Error 1'),
            new Response(500, [], 'Error 2'),
            new Response(500, [], 'Error 3'),
            new Response(500, [], 'Error 4')
        ]);

        $service = $this->createTestOrderServiceWithMockClient($mock);

        $params = [
            'test1' => [
                'url' => 'http://test.com/api',
                'params' => ['test' => 'data1']
            ]
        ];

        // 开启重试，但使用安全模式避免异常
        $result = $service->poolCurl($params, 'GET', 5, true, true);

        $this->assertIsArray($result);
        // 在安全模式下，即使失败也会有结果
        if (array_key_exists('test1', $result)) {
            $this->assertNotNull($result['test1']);
        } else {
            // 如果没有结果，说明请求被完全拒绝了，这也是预期的行为
            $this->assertTrue(true);
        }
    }

    /**
     * 测试正常响应不会触发重试
     */
    public function testNoRetryOnSuccess()
    {
        // 模拟：第一次就成功
        $mock = new MockHandler([
            new Response(200, [], '{"success": true}')
        ]);

        $service = $this->createTestOrderServiceWithMockClient($mock);

        $params = [
            'test1' => [
                'url' => 'http://test.com/api',
                'params' => ['test' => 'data1']
            ]
        ];

        // 开启重试
        $result = $service->poolCurl($params, 'GET', 5, true, true);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('test1', $result);
        $this->assertNotNull($result['test1']);
    }

    /**
     * 测试多个并发请求的重试场景
     */
    public function testConcurrentRequestsWithRetry()
    {
        // 模拟：多个请求，有些需要重试，有些直接成功
        $mock = new MockHandler([
            // 第一个请求：直接成功
            new Response(200, [], '{"result": "success1"}'),
            // 第二个请求：第一次失败，第二次成功
            new Response(500, [], 'Server Error'),
            new Response(200, [], '{"result": "success2"}')
        ]);

        $service = $this->createTestOrderServiceWithMockClient($mock);

        $params = [
            'test1' => [
                'url' => 'http://test.com/api1',
                'params' => ['test' => 'data1']
            ],
            'test2' => [
                'url' => 'http://test.com/api2',
                'params' => ['test' => 'data2']
            ]
        ];

        // 开启重试
        $result = $service->poolCurl($params, 'GET', 5, true, true);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('test1', $result);
        $this->assertArrayHasKey('test2', $result);
        $this->assertNotNull($result['test1']);
        $this->assertNotNull($result['test2']);
    }
}
