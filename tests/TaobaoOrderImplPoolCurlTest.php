<?php

use App\Services\Order\Impl\TaobaoOrderImpl;
use App\Services\Order\Request\OrderDeliveryRequest;
use App\Models\Shop;
use GuzzleHttp\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Log;

/**
 * TaobaoOrderImpl poolCurl 方法测试
 */
class TaobaoOrderImplPoolCurlTest extends TestCase
{
    /**
     * 创建测试用的 TaobaoOrderImpl 实例
     */
    private function createTaobaoOrderService($mockHandler = null)
    {
        $service = new class($mockHandler) extends TaobaoOrderImpl {
            private $mockHandler;

            public function __construct($mockHandler = null) {
                $this->mockHandler = $mockHandler;
                parent::__construct();
            }

            // 重写 poolCurl 方法以支持 Mock
            public function poolCurl($params = [], $method = 'GET', $timeout = null, $safe = false, $autoRetry = false)
            {
                if ($this->mockHandler) {
                    return $this->mockPoolCurl($params, $method, $timeout, $safe, $autoRetry);
                }
                return parent::poolCurl($params, $method, $timeout, $safe, $autoRetry);
            }

            // Mock 版本的 poolCurl
            private function mockPoolCurl($params, $method, $timeout, $safe, $autoRetry)
            {
                $headers = array();
                $keyArr = array_keys($params);
                switch ($method) {
                    case 'get':
                        $postKey = 'query';
                        break;
                    case 'post':
                        $postKey = 'json';
                        $headers = [
                            'Content-type' => 'application/json',
                            "Accept" => "application/json"
                        ];
                        break;
                    case 'post_form':
                        $method = 'post';
                        $postKey = 'form_params';
                        break;
                    default:
                        $postKey = 'json';
                        break;
                }

                $clientConfig = [];
                if (isset($timeout)) {
                    $clientConfig['timeout'] = $timeout;
                }

                $handlerStack = HandlerStack::create($this->mockHandler);
                if ($autoRetry) {
                    $handlerStack->push(\GuzzleHttp\Middleware::retry($this->getRetryDecider(), $this->getRetryDelay()));
                }

                $clientConfig['handler'] = $handlerStack;
                $client = new Client($clientConfig);

                $requests = function ($data) use ($client, $method, $headers, $postKey) {
                    foreach ($data as $index => $datum) {
                        yield function () use ($client, $method, $postKey, $headers, $datum) {
                            return $client->requestAsync($method, $datum['url'], [
                                $postKey => $datum['params'],
                                'headers' => $headers
                            ]);
                        };
                    }
                };

                $result = [];
                $pool = new \GuzzleHttp\Pool($client, $requests($params), [
                    'concurrency' => $this->concurrency,
                    'fulfilled' => function (Response $response, $index) use (&$result, $params, $keyArr) {
                        $orderIdStr = $keyArr[$index];
                        $res = [
                            'http_code' => $response->getStatusCode(),
                            'reason' => $response->getReasonPhrase(),
                            'data' => $response->getBody()
                        ];

                        $content = $this->handleResponse($res, true);
                        $result[$orderIdStr] = [
                            'sort' => $index,
                            'orderIdStr' => $orderIdStr,
                            'content' => $content,
                        ];
                        return $result;
                    },
                    'rejected' => function (\Exception $e, $index) use ($safe, $keyArr) {
                        $orderIdStr = $keyArr[$index];
                        if ($safe) {
                            \Log::warning("遇到异常了，记录一下", ["orderIdStr" => $orderIdStr, "message" => $e->getMessage()]);
                        } else {
                            throw new \Exception($e->getMessage());
                        }
                    },
                ]);

                $promise = $pool->promise();
                $promise->wait();

                array_multisort(array_column($result, 'sort'), SORT_ASC, $result);
                $result = \Illuminate\Support\Arr::pluck($result, 'content', 'orderIdStr');

                return $result;
            }

            // 公开测试方法
            public function testPoolCurlMethod($params, $method = 'post_form', $timeout = null, $safe = false, $autoRetry = false)
            {
                return $this->poolCurl($params, $method, $timeout, $safe, $autoRetry);
            }
        };

        return $service;
    }

    /**
     * 测试淘宝订单批量修改备注的 poolCurl 使用
     */
    public function testBatchEditSellerRemarkPoolCurl()
    {
        // 模拟成功响应
        $mock = new MockHandler([
            new Response(200, [], json_encode([
                'trade_memo_update_response' => [
                    'trade' => ['modified' => '2024-01-15 10:30:00']
                ]
            ])),
            new Response(200, [], json_encode([
                'trade_memo_update_response' => [
                    'trade' => ['modified' => '2024-01-15 10:30:01']
                ]
            ]))
        ]);

        $service = $this->createTaobaoOrderService($mock);

        // 模拟淘宝 API 请求参数
        $params = [
            0 => [
                'url' => 'https://eco.taobao.com/router/rest',
                'params' => [
                    'method' => 'taobao.trade.memo.update',
                    'tid' => '123456789',
                    'memo' => '测试备注1',
                    'flag' => '1'
                ],
                'tid' => '123456789'
            ],
            1 => [
                'url' => 'https://eco.taobao.com/router/rest',
                'params' => [
                    'method' => 'taobao.trade.memo.update',
                    'tid' => '987654321',
                    'memo' => '测试备注2',
                    'flag' => '2'
                ],
                'tid' => '987654321'
            ]
        ];

        // 测试不开启重试
        $result = $service->testPoolCurlMethod($params, 'post_form', 30, false, false);

        $this->assertIsArray($result);
        $this->assertCount(2, $result);
        $this->assertArrayHasKey(0, $result);
        $this->assertArrayHasKey(1, $result);
    }

    /**
     * 测试淘宝订单批量发货的 poolCurl 使用（开启重试）
     */
    public function testBatchDeliveryOrdersPoolCurlWithRetry()
    {
        // 模拟：第一个请求失败后重试成功，第二个请求直接成功
        $mock = new MockHandler([
            // 第一个请求：先失败再成功
            new Response(500, [], 'Internal Server Error'),
            new Response(200, [], json_encode([
                'alibaba_ascp_logistics_offline_send_response' => [
                    'result' => ['success' => true]
                ]
            ])),
            // 第二个请求：直接成功
            new Response(200, [], json_encode([
                'alibaba_ascp_logistics_offline_send_response' => [
                    'result' => ['success' => true]
                ]
            ]))
        ]);

        $service = $this->createTaobaoOrderService($mock);

        // 模拟淘宝发货 API 请求参数
        $params = [
            0 => [
                'url' => 'https://eco.taobao.com/router/rest',
                'params' => [
                    'method' => 'alibaba.ascp.logistics.offline.send',
                    'tid' => '123456789',
                    'logistics_code' => 'YTO',
                    'invoice_no' => 'YT1234567890'
                ]
            ],
            1 => [
                'url' => 'https://eco.taobao.com/router/rest',
                'params' => [
                    'method' => 'alibaba.ascp.logistics.offline.send',
                    'tid' => '987654321',
                    'logistics_code' => 'STO',
                    'invoice_no' => 'ST0987654321'
                ]
            ]
        ];

        // 测试开启重试功能
        $result = $service->testPoolCurlMethod($params, 'post_form', 30, true, true);

        $this->assertIsArray($result);
        $this->assertCount(2, $result);
        $this->assertArrayHasKey(0, $result);
        $this->assertArrayHasKey(1, $result);
    }

    /**
     * 测试淘宝物流跟踪查询的 poolCurl 使用
     */
    public function testLogisticsTraceQueryPoolCurl()
    {
        // 模拟物流跟踪查询响应
        $mock = new MockHandler([
            new Response(200, [], json_encode([
                'logistics_trace_get_response' => [
                    'result' => [
                        'transit_step_result' => [
                            [
                                'status' => 'SIGN',
                                'status_time' => '2024-01-15 10:30:00',
                                'remark' => '已签收'
                            ]
                        ]
                    ]
                ]
            ])),
            new Response(200, [], json_encode([
                'logistics_trace_get_response' => [
                    'result' => [
                        'transit_step_result' => [
                            [
                                'status' => 'TRANSPORT',
                                'status_time' => '2024-01-15 09:30:00',
                                'remark' => '运输中'
                            ]
                        ]
                    ]
                ]
            ]))
        ]);

        $service = $this->createTaobaoOrderService($mock);

        // 模拟物流跟踪查询参数
        $params = [
            0 => [
                'url' => 'https://eco.taobao.com/router/rest',
                'params' => [
                    'method' => 'taobao.logistics.trace.get',
                    'tid' => '123456789',
                    'logistics_code' => 'YTO'
                ]
            ],
            1 => [
                'url' => 'https://eco.taobao.com/router/rest',
                'params' => [
                    'method' => 'taobao.logistics.trace.get',
                    'tid' => '987654321',
                    'logistics_code' => 'STO'
                ]
            ]
        ];

        // 测试安全模式
        $result = $service->testPoolCurlMethod($params, 'post_form', 30, true, false);

        $this->assertIsArray($result);
        $this->assertCount(2, $result);
    }

    /**
     * 测试连接异常时的重试日志记录
     */
    public function testPoolCurlRetryLogging()
    {
        // Mock Log facade - 包括所有可能的日志调用
        Log::shouldReceive('info')
            ->with('poolCurl 开始重试', \Mockery::type('array'))
            ->once();

        Log::shouldReceive('info')
            ->with('poolCurl 重试延迟', \Mockery::type('array'))
            ->once();

        Log::shouldReceive('warning')
            ->withAnyArgs()
            ->zeroOrMoreTimes();

        // 模拟连接异常后重试成功
        $mock = new MockHandler([
            new ConnectException('Connection failed', new Request('POST', 'test')),
            new Response(200, [], json_encode(['success' => true]))
        ]);

        $service = $this->createTaobaoOrderService($mock);

        $params = [
            0 => [
                'url' => 'https://eco.taobao.com/router/rest',
                'params' => [
                    'method' => 'taobao.trade.get',
                    'tid' => '123456789'
                ]
            ]
        ];

        // 开启重试，应该会记录重试日志
        $result = $service->testPoolCurlMethod($params, 'post_form', 30, true, true);

        $this->assertIsArray($result);
        $this->assertArrayHasKey(0, $result);
    }

    /**
     * 测试不同 HTTP 方法的 poolCurl 使用
     */
    public function testDifferentHttpMethods()
    {
        $mock = new MockHandler([
            new Response(200, [], json_encode(['method' => 'GET'])),
            new Response(200, [], json_encode(['method' => 'POST'])),
            new Response(200, [], json_encode(['method' => 'POST_FORM']))
        ]);

        $service = $this->createTaobaoOrderService($mock);

        $params = [
            0 => [
                'url' => 'https://api.test.com',
                'params' => ['test' => 'data']
            ]
        ];

        // 测试 GET 方法
        $result1 = $service->testPoolCurlMethod($params, 'get', 30, true, false);
        $this->assertIsArray($result1);

        // 测试 POST 方法
        $result2 = $service->testPoolCurlMethod($params, 'post', 30, true, false);
        $this->assertIsArray($result2);

        // 测试 POST_FORM 方法（淘宝常用）
        $result3 = $service->testPoolCurlMethod($params, 'post_form', 30, true, false);
        $this->assertIsArray($result3);
    }

    public function tearDown(): void
    {
        \Mockery::close();
        parent::tearDown();
    }
}
