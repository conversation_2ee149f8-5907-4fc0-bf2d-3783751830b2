<?php

use Guzzle<PERSON>ttp\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Request;
use App\Services\Order\AbstractOrderService;
use Illuminate\Support\Facades\Log;

/**
 * poolCurl 方法重试日志记录测试
 */
class PoolCurlRetryLoggingTest extends TestCase
{
    /**
     * 创建测试用的 OrderService 实现类
     */
    private function createTestOrderService()
    {
        return new class extends AbstractOrderService {
            protected $platformType = 'test';

            // 实现所有抽象方法（简单实现）
            public function formatToOrder(array $trade): array { return []; }
            public function formatToAfterSale(array $trade) { return []; }
            public function formatToOrders(array $orders): array { return []; }
            public function formatToGoods(array $goods): array { return []; }
            protected function sendGetTradesOrders(int $startTime, int $endTime, bool $isFirstPull = false) { return []; }
            protected function sendGetOrderInfo(string $tid) { return []; }
            protected function sendGetAfterSaleOrderInfo(string $tid) { return []; }
            protected function sendGetGoods(int $pageSize, int $currentPage) { return []; }
            protected function deliverySellerOrders(string $tid, string $expressCode, string $expressNo, array $orderItemOId, bool $silent = true) { return []; }
            protected function deliverySellerOrdersForOpenApi(string $tid, string $expressCode, string $expressNo, array $orderItemOId) { return []; }
            protected function sendGetTradesOrdersByIncr(int $startTime, int $endTime, bool $isFirstPull = false): array { return []; }
            public function sendServiceInfo(): array { return []; }
            protected function sendDecrypt($order) { return []; }
            protected function sendBatchEncrypt($order) { return []; }
            public function checkAuthStatus(): bool { return true; }
            protected function getClient() { return null; }
            public function openSubscribeMsg(): bool { return true; }
            public function consumeSubscribeMsg() { return []; }
            public function confirmSubscribeMsg(array $idArr) { return []; }
            public function handleSubscribeMsg($data) { return []; }
            public function createWebsocketConnection() { return null; }
            public function batchGetRefundApplyList($data) { return []; }
            protected function sendRefundOrders(int $startTime, int $endTime) { return []; }
            protected function formatRefundOrder($order) { return []; }
            public function sendEditSellerRemark($tid, $sellerFlag, $sellerMemo): bool { return true; }
            public function sendServiceOrderList($beginAt, $endAt) { return []; }
            protected function sendRefundOrderInfo($afterSaleId) { return []; }
            protected function sendRefundOrderList($tid) { return []; }
            public function batchDeliveryOrders(array $orderDeliveryRequests): array { return []; }
            public function sendFactoryShopRoleType(): int { return 1; }
            public function getFactoryTradesOrder(int $startTime, int $endTime) { return []; }
            public function batchGetFactoryOrderInfo($orders) { return []; }
            public function batchReturnFactoryOrder($orders) { return []; }
            public function batchWaybillRecoveryFactoryOrder($waybills) { return []; }
            public function sendGetSellerList() { return []; }
            public function sendQueryTradeTid(array $query_list) { return []; }
            public function sendAddress(): array { return []; }
            public function getQueryTradeOrderId(string $type, string $search): array { return []; }
            public function fillApiParamByOrder($apiMethod, $apiParams, $order, $orderShop = null): array { return []; }
            public function sendByCustom($requestMethod, $apiMethod, $apiParams, $order) { return []; }
        };
    }

    /**
     * 测试重试决策器的日志记录
     */
    public function testRetryDeciderLogging()
    {
        // Mock Log facade
        Log::shouldReceive('info')
            ->with('poolCurl 开始重试', \Mockery::type('array'))
            ->times(2); // 预期会有2次重试日志

        Log::shouldReceive('info')
            ->with('poolCurl 重试已达到最大次数', \Mockery::type('array'))
            ->once(); // 预期会有1次达到最大重试次数的日志

        $service = $this->createTestOrderService();

        // 使用反射获取私有方法
        $reflection = new ReflectionClass($service);
        $method = $reflection->getMethod('getRetryDecider');
        $method->setAccessible(true);

        $retryDecider = $method->invoke($service);
        $request = new Request('GET', 'http://test.com');

        // 模拟第1次重试（连接异常）
        $connectException = new ConnectException('Connection failed', $request);
        $result1 = $retryDecider(0, $request, null, $connectException);
        $this->assertTrue($result1);

        // 模拟第2次重试（服务器错误）
        $response = new Response(500, [], 'Internal Server Error');
        $result2 = $retryDecider(1, $request, $response, null);
        $this->assertTrue($result2);

        // 模拟第3次重试（超过最大次数）
        $result3 = $retryDecider(3, $request, $response, null);
        $this->assertFalse($result3);
    }

    /**
     * 测试重试延迟的日志记录
     */
    public function testRetryDelayLogging()
    {
        // Mock Log facade
        Log::shouldReceive('info')
            ->with('poolCurl 重试延迟', \Mockery::type('array'))
            ->times(3); // 预期会有3次延迟日志

        $service = $this->createTestOrderService();

        // 使用反射获取私有方法
        $reflection = new ReflectionClass($service);
        $method = $reflection->getMethod('getRetryDelay');
        $method->setAccessible(true);

        $retryDelay = $method->invoke($service);

        // 测试不同重试次数的延迟
        $delay1 = $retryDelay(1);
        $this->assertEquals(1000, $delay1);

        $delay2 = $retryDelay(2);
        $this->assertEquals(2000, $delay2);

        $delay3 = $retryDelay(3);
        $this->assertEquals(3000, $delay3);
    }

    /**
     * 测试不同重试原因的日志记录
     */
    public function testDifferentRetryReasons()
    {
        $service = $this->createTestOrderService();

        // 使用反射获取私有方法
        $reflection = new ReflectionClass($service);
        $method = $reflection->getMethod('getRetryDecider');
        $method->setAccessible(true);

        $retryDecider = $method->invoke($service);
        $request = new Request('GET', 'http://test.com');

        // 测试连接异常的日志
        Log::shouldReceive('info')
            ->with('poolCurl 开始重试', \Mockery::on(function ($context) {
                return strpos($context['reason'], '连接异常') !== false;
            }))
            ->once();

        $connectException = new ConnectException('Connection timeout', $request);
        $result1 = $retryDecider(0, $request, null, $connectException);
        $this->assertTrue($result1);

        // 测试超时异常的日志
        Log::shouldReceive('info')
            ->with('poolCurl 开始重试', \Mockery::on(function ($context) {
                return strpos($context['reason'], '超时异常') !== false;
            }))
            ->once();

        $timeoutException = new RequestException('Request timeout occurred', $request);
        $result2 = $retryDecider(0, $request, null, $timeoutException);
        $this->assertTrue($result2);

        // 测试服务器错误的日志
        Log::shouldReceive('info')
            ->with('poolCurl 开始重试', \Mockery::on(function ($context) {
                return strpos($context['reason'], '服务器错误') !== false;
            }))
            ->once();

        $response = new Response(502, [], 'Bad Gateway');
        $result3 = $retryDecider(0, $request, $response, null);
        $this->assertTrue($result3);
    }

    /**
     * 测试日志内容的完整性
     */
    public function testLogContentCompleteness()
    {
        $service = $this->createTestOrderService();

        // 使用反射获取私有方法
        $reflection = new ReflectionClass($service);
        $method = $reflection->getMethod('getRetryDecider');
        $method->setAccessible(true);

        $retryDecider = $method->invoke($service);
        $request = new Request('POST', 'https://api.example.com/orders');

        // 验证日志包含所有必要信息
        Log::shouldReceive('info')
            ->with('poolCurl 开始重试', \Mockery::on(function ($context) {
                return isset($context['retry_count']) &&
                       isset($context['max_retries']) &&
                       isset($context['url']) &&
                       isset($context['method']) &&
                       isset($context['reason']) &&
                       isset($context['next_retry_delay_ms']) &&
                       $context['retry_count'] === 1 &&
                       $context['max_retries'] === 3 &&
                       $context['url'] === 'https://api.example.com/orders' &&
                       $context['method'] === 'POST' &&
                       $context['next_retry_delay_ms'] === 1000;
            }))
            ->once();

        $connectException = new ConnectException('Network error', $request);
        $result = $retryDecider(0, $request, null, $connectException);
        $this->assertTrue($result);
    }

    public function tearDown(): void
    {
        \Mockery::close();
        parent::tearDown();
    }
}
