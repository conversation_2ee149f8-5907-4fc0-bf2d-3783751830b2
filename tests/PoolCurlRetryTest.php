<?php

use Guzzle<PERSON>ttp\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Request;
use App\Services\Order\AbstractOrderService;

/**
 * poolCurl 方法自动重试功能测试
 */
class PoolCurlRetryTest extends TestCase
{
    /**
     * 创建测试用的 OrderService 实现类
     */
    private function createTestOrderService()
    {
        return new class extends AbstractOrderService {
            protected $platformType = 'test';

            // 实现所有抽象方法（简单实现）
            public function formatToOrder(array $trade): array { return []; }
            public function formatToAfterSale(array $trade) { return []; }
            public function formatToOrders(array $orders): array { return []; }
            public function formatToGoods(array $goods): array { return []; }
            protected function sendGetTradesOrders(int $startTime, int $endTime, bool $isFirstPull = false) { return []; }
            protected function sendGetOrderInfo(string $tid) { return []; }
            protected function sendGetAfterSaleOrderInfo(string $tid) { return []; }
            protected function sendGetGoods(int $pageSize, int $currentPage) { return []; }
            protected function deliverySellerOrders(string $tid, string $expressCode, string $expressNo, array $orderItemOId, bool $silent = true) { return []; }
            protected function deliverySellerOrdersForOpenApi(string $tid, string $expressCode, string $expressNo, array $orderItemOId) { return []; }
            protected function sendGetTradesOrdersByIncr(int $startTime, int $endTime, bool $isFirstPull = false): array { return []; }
            public function sendServiceInfo(): array { return []; }
            protected function sendDecrypt($order) { return []; }
            protected function sendBatchEncrypt($order) { return []; }
            public function checkAuthStatus(): bool { return true; }
            protected function getClient() { return null; }
            public function openSubscribeMsg(): bool { return true; }
            public function consumeSubscribeMsg() { return []; }
            public function confirmSubscribeMsg(array $idArr) { return []; }
            public function handleSubscribeMsg($data) { return []; }
            public function createWebsocketConnection() { return null; }
            public function batchGetRefundApplyList($data) { return []; }
            protected function sendRefundOrders(int $startTime, int $endTime) { return []; }
            protected function formatRefundOrder($order) { return []; }
            public function sendEditSellerRemark($tid, $sellerFlag, $sellerMemo): bool { return true; }
            public function sendServiceOrderList($beginAt, $endAt) { return []; }
            protected function sendRefundOrderInfo($afterSaleId) { return []; }
            protected function sendRefundOrderList($tid) { return []; }
            public function batchDeliveryOrders(array $orderDeliveryRequests): array { return []; }
            public function sendFactoryShopRoleType(): int { return 1; }
            public function getFactoryTradesOrder(int $startTime, int $endTime) { return []; }
            public function batchGetFactoryOrderInfo($orders) { return []; }
            public function batchReturnFactoryOrder($orders) { return []; }
            public function batchWaybillRecoveryFactoryOrder($waybills) { return []; }
            public function sendGetSellerList() { return []; }
            public function sendQueryTradeTid(array $query_list) { return []; }
            public function sendAddress(): array { return []; }
            public function getQueryTradeOrderId(string $type, string $search): array { return []; }
            public function fillApiParamByOrder($apiMethod, $apiParams, $order, $orderShop = null): array { return []; }
            public function sendByCustom($requestMethod, $apiMethod, $apiParams, $order) { return []; }
        };
    }

    /**
     * 测试不开启自动重试的默认行为
     */
    public function testPoolCurlWithoutRetry()
    {
        $service = $this->createTestOrderService();

        $params = [
            'test1' => [
                'url' => 'https://httpbin.org/status/200',
                'params' => ['test' => 'data1']
            ]
        ];

        // 不开启重试（默认行为）
        $result = $service->poolCurl($params, 'GET', 5, true, false);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('test1', $result);
    }

    /**
     * 测试开启自动重试功能
     */
    public function testPoolCurlWithRetry()
    {
        $service = $this->createTestOrderService();

        $params = [
            'test1' => [
                'url' => 'https://httpbin.org/status/200',
                'params' => ['test' => 'data1']
            ]
        ];

        // 开启自动重试
        $result = $service->poolCurl($params, 'GET', 5, true, true);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('test1', $result);
    }

    /**
     * 测试重试决策器的逻辑
     */
    public function testRetryDecider()
    {
        $service = $this->createTestOrderService();

        // 使用反射获取私有方法
        $reflection = new ReflectionClass($service);
        $method = $reflection->getMethod('getRetryDecider');
        $method->setAccessible(true);

        $retryDecider = $method->invoke($service);

        // 测试超过最大重试次数
        $result = $retryDecider(3, new Request('GET', 'http://test.com'), null, null);
        $this->assertFalse($result);

        // 测试连接异常应该重试
        $connectException = new ConnectException('Connection failed', new Request('GET', 'http://test.com'));
        $result = $retryDecider(1, new Request('GET', 'http://test.com'), null, $connectException);
        $this->assertTrue($result);

        // 测试超时异常应该重试
        $timeoutException = new RequestException('timeout occurred', new Request('GET', 'http://test.com'));
        $result = $retryDecider(1, new Request('GET', 'http://test.com'), null, $timeoutException);
        $this->assertTrue($result);

        // 测试5xx状态码应该重试
        $response = new Response(500);
        $result = $retryDecider(1, new Request('GET', 'http://test.com'), $response, null);
        $this->assertTrue($result);

        // 测试正常响应不应该重试
        $response = new Response(200);
        $result = $retryDecider(1, new Request('GET', 'http://test.com'), $response, null);
        $this->assertFalse($result);
    }

    /**
     * 测试重试延迟逻辑
     */
    public function testRetryDelay()
    {
        $service = $this->createTestOrderService();

        // 使用反射获取私有方法
        $reflection = new ReflectionClass($service);
        $method = $reflection->getMethod('getRetryDelay');
        $method->setAccessible(true);

        $retryDelay = $method->invoke($service);

        // 测试重试延迟递增
        $this->assertEquals(1000, $retryDelay(1)); // 第1次重试：1秒
        $this->assertEquals(2000, $retryDelay(2)); // 第2次重试：2秒
        $this->assertEquals(3000, $retryDelay(3)); // 第3次重试：3秒
    }

    /**
     * 测试方法参数的向后兼容性
     */
    public function testBackwardCompatibility()
    {
        $service = $this->createTestOrderService();

        $params = [
            'test1' => [
                'url' => 'https://httpbin.org/status/200',
                'params' => ['test' => 'data1']
            ]
        ];

        // 测试原有的方法调用方式仍然有效
        $result1 = $service->poolCurl($params);
        $result2 = $service->poolCurl($params, 'GET');
        $result3 = $service->poolCurl($params, 'GET', 30);
        $result4 = $service->poolCurl($params, 'GET', 30, false);

        $this->assertIsArray($result1);
        $this->assertIsArray($result2);
        $this->assertIsArray($result3);
        $this->assertIsArray($result4);
    }

    /**
     * 测试不同HTTP方法的重试功能
     */
    public function testRetryWithDifferentMethods()
    {
        $service = $this->createTestOrderService();

        $params = [
            'test1' => [
                'url' => 'https://httpbin.org/status/200',
                'params' => ['test' => 'data1']
            ]
        ];

        // 测试GET方法
        $result1 = $service->poolCurl($params, 'GET', 5, true, true);
        $this->assertIsArray($result1);

        // 测试POST方法
        $result2 = $service->poolCurl($params, 'POST', 5, true, true);
        $this->assertIsArray($result2);

        // 测试POST_FORM方法
        $result3 = $service->poolCurl($params, 'post_form', 5, true, true);
        $this->assertIsArray($result3);
    }
}
