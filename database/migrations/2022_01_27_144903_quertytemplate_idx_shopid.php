<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class QuertytemplateIdxShopid extends Migration
{
    private  $idx_shopid = 'idx_shopid';
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('query_templates', function (Blueprint $table) {

            $table->index(['shop_id'], $this->idx_shopid);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('query_templates', function (Blueprint $table) {
            $table->dropIndex($this->idx_shopid);
        });
    }
}
