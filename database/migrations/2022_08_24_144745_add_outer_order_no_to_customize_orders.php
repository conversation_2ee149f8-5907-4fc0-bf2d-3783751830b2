<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddOuterOrderNoToCustomizeOrders extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('customize_orders', function (Blueprint $table) {
            $table->string('outer_order_no', 100)->default('')->comment('外部订单号');
        });
        Schema::table('print_records', function (Blueprint $table) {
            $table->string('outer_order_no', 100)->default('')->comment('外部订单号');
        });
        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->string('outer_order_no', 100)->default('')->comment('外部订单号');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('customize_orders', function (Blueprint $table) {
            $table->dropColumn('outer_order_no');
        });
        Schema::table('print_records', function (Blueprint $table) {
            $table->dropColumn('outer_order_no');
        });
        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->dropColumn('outer_order_no');
        });
    }
}
