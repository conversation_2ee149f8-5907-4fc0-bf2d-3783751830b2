<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterTableDeliveryRecords20230612100911 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('delivery_records', function (Blueprint $table) {
            $table->index(['order_id'], 'idx_orderid');
            $table->index(['order_no'], 'idx_orderno');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('delivery_records', function (Blueprint $table) {
            //
        });
    }
}
