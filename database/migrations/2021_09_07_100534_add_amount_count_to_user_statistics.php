<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddAmountCountToUserStatistics extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_statistics', function (Blueprint $table) {
            $table->Integer('free_to_pay_count')->default(0)->comment('免费转付费数')->after('expired_count');
            $table->Integer('payment_amount_count')->default(0)->comment('总付费金额')->after('free_to_pay_count');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_statistics', function (Blueprint $table) {
            $table->dropColumn('free_to_pay_count');
            $table->dropColumn('payment_amount_count');
        });
    }
}
