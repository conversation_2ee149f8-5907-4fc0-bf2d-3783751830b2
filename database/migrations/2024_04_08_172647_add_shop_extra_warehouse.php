<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddShopExtraWarehouse extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('shop_extras', function (Blueprint $table) {
            $table->string('warehouse_name')->nullable()->comment('仓库名称');
            $table->string('waybill_extra_province')->nullable()->comment('快递加收省份');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('shop_extras', function (Blueprint $table) {
            //
        });
    }
}
