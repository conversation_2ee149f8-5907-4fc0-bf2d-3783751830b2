<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddOpenUserIdToUsersTable extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('open_user_id', 100)->nullable()->comment('开放平台用户ID')->after('invite_code');
            $table->string('open_token', 100)->nullable()->comment('开放平台用户TOKEN')->after('open_user_id');

            $table->unique('open_user_id');
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('open_user_id');
        });
    }
}
