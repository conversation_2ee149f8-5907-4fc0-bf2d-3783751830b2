<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateWaybillShareActionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('waybill_share_actions', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('shop_id');
            $table->integer('company_id')->comment('快递公司id');
            $table->string('shop_name', 255)->nullable()->comment('店铺名');
            $table->string('name', 100)->nullable()->comment('用户名');
            $table->string('identifier', 128)->comment('唯一身份');
            $table->string('branch_name', 255)->nullable()->comment('网点名称');
            $table->string('wp_code', 64)->comment('快递公司ID');
            $table->string('wp_name', 64)->comment('快递公司名称');
            $table->integer('balanceLimit')->default(0)->comment('单号数量');
            $table->string('province')->nullable()->comment('省');
            $table->string('city')->nullable()->comment('市');
            $table->string('district')->nullable()->comment('区');
            $table->string('detail')->nullable()->comment('详细地址');
            $table->tinyInteger('action')->default(4)->comment('操作状态，0是恢复，1是冻结，2是删除，3是追加，4是新建，5是减少');
            $table->timestamps();
            $table->index(['user_id', 'shop_id']);
            $table->comment = '分享面单操作记录表';
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('waybill_share_actions');
    }
}
