<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class OperationlogAddCreateadindx extends Migration
{
    private $idxCreatedat = 'idx_createdat';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('operation_logs', function (Blueprint $table) {
            //

            $table->index(['created_at'], $this->idxCreatedat);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('operation_logs', function (Blueprint $table) {
            //
            $table->dropIndex($this->idxCreatedat);
        });
    }
}
