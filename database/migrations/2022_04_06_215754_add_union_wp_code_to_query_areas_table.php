<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddUnionWpCodeToQueryAreasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('query_areas', function (Blueprint $table) {
            $table->string('union_wp_code')->default('')->comment('快递公司编码');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('query_areas', function (Blueprint $table) {
            $table->dropColumn('union_wp_code');
        });
    }
}
