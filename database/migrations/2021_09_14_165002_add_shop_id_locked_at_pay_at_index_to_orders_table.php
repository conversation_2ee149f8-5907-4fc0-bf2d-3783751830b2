<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddShopIdLockedAtPayAtIndexToOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->index(['shop_id', 'receiver_phone', 'pay_at']);
            $table->index(["shop_id", "locked_at", "pay_at"]);
        });
        Schema::table('shops', function (Blueprint $table) {
            $table->index("created_at");
        });
        Schema::table('user_extras', function (Blueprint $table) {
            $table->index(["shop_id", "version", "deleted_at",]);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex(['shop_id', 'receiver_phone', 'pay_at']);
            $table->dropIndex(["shop_id", "locked_at", "pay_at"]);
        });
        Schema::table('shops', function (Blueprint $table) {
            $table->dropIndex("created_at");
        });
        Schema::table('user_extras', function (Blueprint $table) {
            $table->dropIndex(["shop_id", "version", "deleted_at",]);
        });
    }
}
