<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddIsCommentToOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
	    Schema::table('orders', function (Blueprint $table) {
		    $table->tinyInteger('is_comment')->default(0)->comment('是否评价 (1:已评价)')->after('pay_at');
	    });
	    Schema::table('order_items', function (Blueprint $table) {
		    $table->tinyInteger('is_comment')->default(0)->comment('是否评价 (1:已评价)')->after('refund_status');
	    });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
	    Schema::table('orders', function (Blueprint $table) {
		    $table->dropColumn('is_comment');
	    });
	    Schema::table('order_items', function (Blueprint $table) {
		    $table->dropColumn('is_comment');
	    });
    }
}
