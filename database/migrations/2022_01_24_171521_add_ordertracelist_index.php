<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddOrdertracelistIndex extends Migration
{
    private $idx_shopid_status_sendat='idx_shopid_status_sendat';
    private $idx_shopid_sendat="idx_shopid_sendat";

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_trace_list', function (Blueprint $table) {
            $table->index(['shop_id', 'status','send_at'], $this->idx_shopid_status_sendat);
            $table->index(['shop_id', 'send_at'], $this->idx_shopid_sendat);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_trace_list', function (Blueprint $table) {
            $table->dropIndex($this->idx_shopid_status_sendat);
            $table->dropIndex($this->idx_shopid_sendat);
        });
    }
}
