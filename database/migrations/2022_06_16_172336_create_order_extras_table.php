<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateOrderExtrasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_extras', function (Blueprint $table) {
            $table->increments('id');
            $table->bigInteger('order_id');
            $table->string('assign_cancel_reason')->default('')->comment('分配取消原因');
            $table->tinyInteger('is_factory_shipped')->default(0)->comment('是否厂家发货');
            $table->timestamp('assign_at')->nullable()->comment('分配时间');
            $table->timestamp('assign_cancel_at')->nullable()->comment('分配取消时间');

            $table->timestamps();
            $table->softDeletes();

            $table->unique(['order_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_extras');
    }
}
