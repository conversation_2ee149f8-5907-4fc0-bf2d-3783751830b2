<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterTableCompanyShippingFeeTemplates20240704171810 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('company_shipping_fee_templates', function (Blueprint $table) {
            $table->string('owner_id')->nullable() ;
            $table->string('owner_name')->nullable() ;
            $table->tinyInteger('source')->comment('是否虚拟分享电子面单 0=不，1=是')->nullable() ;
            $table->string('street')->nullable() ->comment('街道') ->change();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('company_shipping_fee_templates', function (Blueprint $table) {
            //
        });
    }
}
