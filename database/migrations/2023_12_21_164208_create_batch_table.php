<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateBatchTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('batch', function (Blueprint $table) {
            $table->increments('id');
            $table->bigInteger('shop_id')->comment('店铺 id');
            $table->string('batch_no',30)->comment('批次号');
            $table->tinyInteger('type')->default(0)->comment('批次业务类型');
            $table->tinyInteger('status')->default(1)->comment('批次状态：0 待执行，1 执行中，2 成功，3 失败');
            $table->integer('total_num')->default(0)->comment('总数量');
            $table->integer('success_num')->default(0)->comment('成功数量');
            $table->integer('ignore_num')->default(0)->comment('忽略数量');
            $table->integer('fail_num')->default(0)->comment('失败数量');
            $table->string('fail_msg')->nullable()->comment('失败原因');
            $table->string('memo')->nullable()->comment('备注');
            $table->timestamps();
            $table->softDeletes();
            $table->unique(['shop_id','type','batch_no']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('batch');
    }
}
