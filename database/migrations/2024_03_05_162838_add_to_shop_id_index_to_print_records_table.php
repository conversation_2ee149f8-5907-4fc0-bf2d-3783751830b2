<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddToShopIdIndexToPrintRecordsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        try {
            Schema::table('print_records', function (Blueprint $table) {
                $table->tinyInteger('order_type')->default(1)->comment('订单类型 1普通订单 2自由打印 3代打订单');
            });
        }catch (\Exception $e) {
            echo $e->getMessage();
        }
        try {
            Schema::table('print_records', function (Blueprint $table) {
                $table->index(['to_shop_id','created_at']);
            });
        }catch (\Exception $e) {
            echo $e->getMessage();
        }

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('print_records', function (Blueprint $table) {
            //
        });
    }
}
