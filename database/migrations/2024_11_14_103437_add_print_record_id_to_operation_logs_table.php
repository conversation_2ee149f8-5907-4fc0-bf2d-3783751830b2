<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPrintRecordIdToOperationLogsTable extends Migration
{
    public function up(): void
    {
        Schema::table('operation_logs', function (Blueprint $table) {
            $table->unsignedBigInteger('print_record_id')->default(0)->after('package_id')->comment('打印记录id');
        });
    }

    public function down(): void
    {
        Schema::table('operation_logs', function (Blueprint $table) {
            //
        });
    }
}
