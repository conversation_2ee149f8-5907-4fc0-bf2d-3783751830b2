<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddLogisticsToOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->string('smart_logistics',15)->default('')->comment('智能物流')->after('is_comment');
            $table->bigInteger('district_code')->default(0)->comment('区域编码')->after('smart_logistics');;

            $table->index(['shop_id','district_code']);
            $table->index(['shop_id','smart_logistics','pay_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('smart_logistics');
            $table->dropColumn('district_code');

            $table->dropIndex(['shop_id','district_code']);
            $table->dropIndex(['shop_id','smart_logistics','pay_at']);
        });
    }
}
