<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterTableSystemConfig20250124141848 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('system_configs')->insert([
            "name"=>'订阅配置',
            "key"=>'subscription_product',
            "value"=>'{"type":"main","versions":[{"name":"标准版","code":"Standard","price":[{"name":"一个月","unit":"month","value":1,"price":"20.00"},{"name":"三个月","unit":"month","value":3,"price":"60.00"},{"name":"六个月","unit":"month","value":6,"price":"100.00"},{"name":"1年","unit":"year","value":1,"price":"200.00"}]},{"name":"高级版","code":"Senior","price":[{"name":"一个月","unit":"month","value":1,"price":"38.00"},{"name":"三个月","unit":"month","value":3,"price":"98.00"},{"name":"六个月","unit":"month","value":6,"price":"166.00"},{"name":"1年","unit":"year","value":1,"price":"258.00"}]}]}'

        ]);

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('system_config', function (Blueprint $table) {
            //
        });
    }
}
