<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class Printrecordidx extends Migration
{
    private $idxShopIdCreateatBatchNoWaybillCodeOrderNo="idx_shopid_createat_batchno_waybillcode_orderno";
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('print_records', function (Blueprint $table) {
            $table->index(['shop_id','created_at','batch_no','waybill_code','order_no'], $this->idxShopIdCreateatBatchNoWaybillCodeOrderNo);
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('print_records', function (Blueprint $table) {
            $table->dropIndex($this->idxShopIdCreateatBatchNoWaybillCodeOrderNo);
        });
    }
}
