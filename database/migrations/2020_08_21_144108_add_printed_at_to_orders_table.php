<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddPrintedAtToOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
	    Schema::table('orders', function (Blueprint $table) {
		    $table->timestamp('printed_at')->nullable()->comment('打印时间')->after('print_status');
	    });
	    Schema::table('shop_extras', function (Blueprint $table) {
		    $table->tinyInteger('merge_order_open')->default(1)->comment('合单开关')->after('needle_items');
		    $table->integer('merge_order_num')->default(10)->comment('合单数量限制')->after('merge_order_open');
	    });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
	    Schema::table('orders', function (Blueprint $table) {
		    $table->dropColumn('printed_at');
	    });
	    Schema::table('shop_extras', function (Blueprint $table) {
		    $table->dropColumn('merge_order_open');
		    $table->dropColumn('merge_order_num');
	    });
    }
}
