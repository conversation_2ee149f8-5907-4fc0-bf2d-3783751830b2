<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateDeliveryRecordsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('delivery_records', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('店铺id');
            $table->integer('order_id')->default(0)->comment('订单表id');
            $table->integer('history_id')->default(0)->comment('取号记录表id');
            $table->string('order_no', 255)->comment('订单编号');
            $table->string('waybill_code', 255)->nullable()->comment('电子面单号');
            $table->string('wp_code', 64)->nullable()->comment('物流公司编码');
            $table->timestamps();
            $table->index(['user_id', 'shop_id']);
            $table->comment = '发货表';
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('delivery_records');
    }
}
