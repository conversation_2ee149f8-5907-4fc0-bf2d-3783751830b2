<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterTableCompanyShippingFeeTemplates20240704164800 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('company_shipping_fee_templates', function (Blueprint $table) {
            $table->dropColumn('company_id');
            $table->string('wp_code')->comment('快递公司编码');
            $table->string('wp_name')->comment('快递公司名称');
            $table->string('branch_name')->comment('快递公司分支名称')->nullable();
            $table->string('branch_code')->comment('快递公司分支编码')->nullable();
            $table->string('province')->comment('省份');
            $table->string('city')->comment('城市');
            $table->string('district')->comment('地区');
            $table->string('street')->comment('街道')->nullable();
            $table->string('detail')->comment('详细地址');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('company_shipping_fee_templates', function (Blueprint $table) {
            //
        });
    }
}
