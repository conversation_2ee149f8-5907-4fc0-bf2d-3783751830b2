<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterWaybillHistoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->tinyInteger('source')->default(0)->comment('是否虚拟分享电子面单 0=不，1=是')->after('waybill_status');
            $table->integer('source_userid')->nullable()->comment('授权分享面单用户id')->after('source');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->dropColumn('source');
            $table->dropColumn('source_userid');
        });
    }
}
