<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateShopsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shops', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('授权记录 ID');
            $table->integer('user_id')->comment('用户id, user主键');
            $table->tinyInteger('type')->default(0)->comment('授权类型,如taoabo');
            $table->string('identifier', 128)->comment('唯一身份');
            $table->string('access_token', 500)->nullable()->comment('授权token');
            $table->string('refresh_token', 500)->nullable()->comment('刷新token');
            $table->timestamp('expire_at')->nullable()->comment('access token过期时间');
            $table->timestamp('auth_at')->nullable()->comment('授权开始时间');
            $table->tinyInteger('auth_status')->default(0)->comment('授权状态');
            $table->integer('login_count')->default(0)->comment('登录次数');
            $table->string('shop_name', 100)->nullable()->comment('店铺名');
            $table->string('shop_logo', 300)->nullable()->comment('店铺LOGO');
            $table->string('name', 100)->nullable()->comment('用户名');
            $table->string('auth_user_id', 64)->nullable()->comment('授权用户id');
            $table->timestamp('last_sync_at')->nullable()->comment('最后同步订单时间');
            $table->timestamp('last_refund_sync_at')->nullable()->comment('最后同步退款订单时间');
            $table->integer('original_user_id')->default(0)->comment('原来的用户ID');
            $table->timestamps();
            $table->softDeletes();
            $table->index('user_id');
            $table->index('auth_at');
            $table->index('type');
            $table->comment = '店铺授权表';
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shops');
    }
}
