<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddTypeToDeliveryRecordsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
	    Schema::table('delivery_records', function (Blueprint $table) {
		    $table->tinyInteger('type')->default(0)->comment('是否为导入发货')->after('wp_code');
		    $table->text('result')->nullable()->comment('是否为导入发货')->after('type');
	    });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
	    Schema::table('delivery_records', function (Blueprint $table) {
		    $table->dropColumn('type');
		    $table->dropColumn('result');
	    });
    }
}
