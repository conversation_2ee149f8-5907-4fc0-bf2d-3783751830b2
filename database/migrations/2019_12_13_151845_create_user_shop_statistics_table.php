<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateUserShopStatisticsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_shop_statistics', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('id');
            $table->integer('user_id')->default(0)->comment('用户id');
            $table->integer('shop_id')->default(0)->comment('店铺id');
            $table->date('stat_at')->nullable()->comment('统计时间');
            $table->integer('interval')->default(0)->comment('间隔：1-天');
            $table->integer('print_count')->default(0)->comment('总计打单数');
            $table->integer('order_count')->default(0)->comment('总计订单数');
            $table->integer('delivery_count')->default(0)->comment('总计发货数');
            $table->integer('address_count')->default(0)->comment('发货地址数量');
            $table->integer('template_count')->default(0)->comment('模板数量');
            $table->timestamps();
            $table->index(['user_id', 'shop_id', 'stat_at', 'interval']);
            $table->comment = '使用情况统计表';
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_shop_statistics');
    }
}
