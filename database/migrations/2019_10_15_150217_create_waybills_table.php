<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateWaybillsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('waybills', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('ID');
            $table->integer('user_id')->comment('用户id, user主键');
            $table->integer('shop_id')->comment('店铺id');
            $table->tinyInteger('auth_source')->default(0)->comment('授权来源');
            $table->string('access_token', 500)->comment('电子面单授权token');
            $table->string('refresh_token', 500)->comment('电子面单刷新token');
            $table->string('owner_id', 255)->comment('owner_id');
            $table->string('owner_name', 255)->comment('owner_name');
            $table->integer('expires_in')->comment('token有效期');
            $table->timestamp('expires_at')->nullable()->comment('token过期时间');
            $table->timestamps();
            $table->softDeletes();
            $table->index(['user_id', 'shop_id', 'auth_source']);
            $table->comment = '电子面单授权表';
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('waybills');
    }
}
