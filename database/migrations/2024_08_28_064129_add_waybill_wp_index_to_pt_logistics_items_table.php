<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddWaybillWpIndexToPtLogisticsItemsTable extends Migration
{
    public function up(): void
    {
        Schema::table('pt_logistics_items', function (Blueprint $table) {
            $table->string('sku_id',64)->nullable()->comment('sku_id')->change();
            $table->string('outer_sku_id',64)->nullable()->comment('sku 编码')->change();
            $table->string('num_iid',64)->nullable()->comment('商品 id')->change();
//            $table->integer('num')->notNull()->comment('发货数量')->after('num_iid');
            $table->string('waybill_wp_index',64)->comment('快递单号-物流公司索引');
            $table->dropUnique(['shop_id','pt_logistics_id','order_item_id']);
            $table->unique(['pt_logistics_id', 'order_item_id']);
            $table->index(['waybill_wp_index']);
            $table->dropColumn('delivery_id');
            $table->dropColumn('delivery_at');
        });
    }

    public function down(): void
    {
        Schema::table('pt_logistics_items', function (Blueprint $table) {
            //
        });
    }
}
