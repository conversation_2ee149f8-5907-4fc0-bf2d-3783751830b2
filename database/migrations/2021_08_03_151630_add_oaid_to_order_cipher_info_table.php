<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddOaidToOrderCipherInfoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_cipher_info', function (Blueprint $table) {
            $table->string('oaid',1024)->default('')->comment('加密id')->after('receiver_address_mask');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_cipher_info', function (Blueprint $table) {
            $table->dropColumn('oaid');
        });
    }
}
