<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddIndexToOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->index('receiver_name');
//            $table->index('seller_memo');
            $table->dropIndex(['send_at','shop_id', 'order_status']);
            $table->index(['shop_id', 'send_at']);
            $table->index(['shop_id', 'printed_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex('receiver_name');
//            $table->dropIndex('seller_memo');
            $table->dropIndex(['shop_id', 'send_at']);
            $table->dropIndex(['shop_id', 'printed_at']);
        });
    }
}
