<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddStatusToOrderItem extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->tinyInteger('status')->default(0)->comment('订单状态');
            $table->string('waybill_code', 100)->nullable()->comment('运单号');
            $table->timestamp('send_at')->nullable()->comment('发货时间');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->dropColumn('status');
            $table->dropColumn('waybill_code');
            $table->dropColumn('send_at');
        });
    }
}
