<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterTableWaybillHistories20240229154507 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        try {
            Schema::table('waybill_histories', function (Blueprint $table) {
                $table->dropIndex('idx_shopid_createdat');
            });
        }catch (\Exception $e) {
            echo $e->getMessage();
        }
        try {
            Schema::table('goods_skus', function (Blueprint $table) {
                $table->dropIndex('idx_goodsid');
            });
        }catch (\Exception $e) {
            echo $e->getMessage();
        }
        try {
            Schema::table('print_records', function (Blueprint $table) {
                $table->dropIndex('print_records_shop_id_created_at_index');
            });

        }catch (\Exception $e){
            echo $e->getMessage();
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('waybill_histories', function (Blueprint $table) {
            //
        });
    }
}
