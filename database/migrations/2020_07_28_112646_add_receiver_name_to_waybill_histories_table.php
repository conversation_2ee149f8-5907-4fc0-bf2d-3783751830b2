<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddReceiverNameToWaybillHistoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->string('receiver_province', 50)->nullable()->comment('收货人省份')->after('waybill_status');
            $table->string('receiver_city', 50)->nullable()->comment('收货人城市')->after('receiver_province');
            $table->string('receiver_district', 50)->nullable()->comment('收货人地区')->after('receiver_city');
            $table->string('receiver_name', 255)->nullable()->comment('收货人名字')->after('receiver_district');
            $table->string('receiver_phone', 255)->nullable()->comment('收货人手机')->after('receiver_name');
            $table->string('receiver_address', 255)->nullable()->comment('收货地址')->after('receiver_phone');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->dropColumn('receiver_province');
            $table->dropColumn('receiver_city');
            $table->dropColumn('receiver_district');
            $table->dropColumn('receiver_name');
            $table->dropColumn('receiver_phone');
            $table->dropColumn('receiver_address');
        });
    }
}
