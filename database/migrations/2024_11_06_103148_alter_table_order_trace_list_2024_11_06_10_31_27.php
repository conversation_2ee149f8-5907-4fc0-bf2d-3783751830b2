<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterTableOrderTraceList20241106103127 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_trace_list', function (Blueprint $table) {
            $table->index(['latest_updated_at','status','created_at'],'idx_latestupdatedat_status_createdat');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_trace_list', function (Blueprint $table) {
            //
        });
    }
}
