<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddWaybillhistoryStaticstisOpt extends Migration
{
    private $idxShopIdCreatedAt = 'idx_shopid_createdat';
    private $idxShopIdPackagedIdCreatedAt = 'idx_shopid_package_id_createdat';
    private $idxShopIdWaybillStatusCreatedAt = 'idx_shopid_waybillstatus_createdat';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->index(['shop_id', 'created_at'], $this->idxShopIdCreatedAt);
            $table->index(['shop_id', 'package_id', 'created_at'], $this->idxShopIdPackagedIdCreatedAt);
            $table->index(['shop_id', 'waybill_status', 'created_at'],$this->idxShopIdWaybillStatusCreatedAt);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->dropIndex($this->idxShopIdCreatedAt);
            $table->dropIndex($this->idxShopIdPackagedIdCreatedAt);
            $table->dropIndex($this->idxShopIdWaybillStatusCreatedAt);
        });
    }
}
