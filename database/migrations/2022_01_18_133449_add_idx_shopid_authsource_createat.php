<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddIdxShopidAuthsourceCreateat extends Migration
{
    private $indexName='idx_shopid_authsource_createat';
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('waybill_histories', function (Blueprint $table)   {

            $table->index(['shop_id','auth_source','created_at'],$this->indexName);

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->dropIndex($this->indexName);
        });
    }
}
