<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDeliveryIdToPtLogisticsTable extends Migration
{
    public function up(): void
    {
        Schema::table('pt_logistics', function (Blueprint $table) {
            $table->string('delivery_id')->nullable()->after('delivery_at')->comment('发货编号');
            $table->string('waybill_wp_index',64)->after('wp_code')->comment('快递单号-物流公司索引');
            $table->dropUnique(['shop_id', 'waybill_code', 'wp_code']);
            $table->unique(['shop_id', 'order_id', 'waybill_wp_index']);
            $table->index(['waybill_wp_index']);
        });
    }

    public function down(): void
    {
        Schema::table('pt_logistics', function (Blueprint $table) {
            //
        });
    }
}
