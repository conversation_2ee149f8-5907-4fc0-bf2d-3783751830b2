<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddPresetLogisticsDistrictVersionToShopExtrasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('shop_extras', function (Blueprint $table) {
            $table->json('preset_logistics_district_data_processed')->comment('根据区域预设快递处理后数据')->after('preset_logistics_district_data');
            $table->tinyInteger('preset_logistics_district_version')->default(1)->comment('区域预设快递版本')->after('preset_logistics_district_data_processed');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('shop_extras', function (Blueprint $table) {
            $table->dropColumn('preset_logistics_district_version');
        });
    }
}
