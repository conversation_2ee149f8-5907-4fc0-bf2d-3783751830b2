<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateOrderCipherInfoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_cipher_info', function (Blueprint $table) {
            $table->increments('id');
            $table->bigInteger('order_id');
            $table->text('receiver_phone_ciphertext')->comment('收货人手机 密文');
            $table->text('receiver_name_ciphertext')->comment('收货人名字 密文');
            $table->text('receiver_address_ciphertext')->comment('收货地址 密文');

            $table->string('receiver_phone_mask')->default('')->comment('收货人手机 脱敏');
            $table->string('receiver_name_mask')->default('')->comment('收货人名字 脱敏');
            $table->string('receiver_address_mask')->default('')->comment('收货地址 脱敏');

            $table->timestamps();
            $table->softDeletes();

            $table->unique('order_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_cipher_info');
    }
}
