<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateShopBindTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shop_binds', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('id');
            $table->integer('f_shop_id')->default(0)->comment('父级店铺id');
            $table->integer('o_shop_id')->default(0)->comment('子级shop_id');
            $table->tinyInteger('type')->default(0)->comment('绑定类型：1兄弟、2主子、3代发');
            $table->timestamps();
            $table->softDeletes();
            $table->index('f_shop_id');
            $table->index('o_shop_id');
            $table->comment = '绑定店铺关系表';
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shop_binds');
    }
}
