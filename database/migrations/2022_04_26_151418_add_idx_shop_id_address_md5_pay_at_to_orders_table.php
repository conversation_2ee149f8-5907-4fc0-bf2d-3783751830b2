<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddIdxShopIdAddressMd5PayAtToOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
//            $table->index(['shop_id', 'address_md5', 'pay_at']);
            $table->index(['address_md5','shop_id','order_status','pay_at','refund_status','send_at','locked_at','printed_at'],'idx_addressmd5_withdata');
            $table->index(['shop_id','order_status','pay_at','refund_status','send_at','locked_at','printed_at','address_md5'],'idx_shopid_orderstatus_payat_addressmd5_withdata');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
//            $table->dropIndex(['shop_id', 'address_md5', 'pay_at']);
            $table->dropIndex('idx_addressmd5_withdata');
            $table->dropIndex('idx_shopid_orderstatus_payat_addressmd5_withdata');
        });
    }
}
