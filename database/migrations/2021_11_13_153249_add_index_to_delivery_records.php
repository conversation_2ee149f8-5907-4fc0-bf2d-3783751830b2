<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddIndexToDeliveryRecords extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('delivery_records', function (Blueprint $table) {
            $table->index(['user_id', 'shop_id', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('delivery_records', function (Blueprint $table) {
            $table->dropIndex(['user_id', 'shop_id', 'created_at']);
        });
    }
}
