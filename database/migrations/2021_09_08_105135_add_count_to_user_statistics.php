<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddCountToUserStatistics extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_statistics', function (Blueprint $table) {
            $table->Integer('pay_user_count')->default(0)->comment('总付费用户数')->after('payment_amount_count');
            $table->Integer('pay_order_count')->default(0)->comment('总付费订单数')->after('pay_user_count');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_statistics', function (Blueprint $table) {
            $table->dropColumn('pay_user_count');
            $table->dropColumn('pay_order_count');
        });
    }
}
