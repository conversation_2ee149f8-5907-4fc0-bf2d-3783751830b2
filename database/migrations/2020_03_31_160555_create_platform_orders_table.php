<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreatePlatformOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('platform_orders', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->tinyInteger('platform_type')->comment('平台类型');
            $table->integer('user_id')->default(0)->comment('用户ID');
            $table->integer('shop_id')->default(0)->comment('店铺ID');
            $table->integer('identifier')->comment('唯一标识');
            $table->string('order_id', 150)->nullable()->comment('订单ID');
            $table->string('order_no', 150)->nullable()->comment('订单编号');
            $table->string('service_id', 200)->nullable()->comment('应用id');
            $table->string('service_name', 200)->nullable()->comment('应用名称');
            $table->integer('fee')->default(0)->comment('原价/单位：分');
            $table->integer('prom_fee')->default(0)->comment('优惠/单位：分');
            $table->integer('refund_fee')->default(0)->comment('退款/单位：分');
            $table->integer('pay_fee')->default(0)->comment('实付/单位：分');
            $table->timestamp('pay_at')->nullable()->comment('订单支付时间');
            $table->timestamp('order_created_at')->nullable()->comment('订单创建时间');
            $table->integer('order_cycle')->default(0)->comment('有效期限');
            $table->timestamp('cycle_start_at')->nullable()->comment('订购周期开始时间');
            $table->timestamp('cycle_end_at')->nullable()->comment('订购周期结束时间');
            $table->tinyInteger('source')->default(0)->comment('订单来源，0-线上订购，1-线下回流');
            $table->string('sku_id', 100)->nullable()->comment('规格id');
            $table->string('sku_spec', 100)->nullable()->comment('商品规格信息');
            $table->timestamps();
            $table->index('platform_type');
            $table->index(['user_id', 'shop_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('platform_orders');
    }
}
