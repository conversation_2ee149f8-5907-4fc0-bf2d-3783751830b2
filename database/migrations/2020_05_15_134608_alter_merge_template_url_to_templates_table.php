<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterMergeTemplateUrlToTemplatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
	    Schema::table('templates', function (Blueprint $table) {
		    $table->text('merge_template_url')->nullable()->change();
	    });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
	    Schema::table('templates', function (Blueprint $table) {
		    $table->string('merge_template_url', 255)->nullable()->change();
	    });
    }
}
