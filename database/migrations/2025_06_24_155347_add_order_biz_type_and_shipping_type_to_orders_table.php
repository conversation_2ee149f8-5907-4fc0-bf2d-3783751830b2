<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddOrderBizTypeAndShippingTypeToOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->tinyInteger('order_biz_type')->default(0)->nullable(false)->comment('订单业务类型：0 普通订单 1 质检 2 虚拟');
            $table->tinyInteger('order_shipping_type')->default(0)->nullable(false)->comment('订单发货类型：0 普通物流发货 1 虚拟发货');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('order_biz_type');
            $table->dropColumn('order_shipping_type');
        });
    }
}
