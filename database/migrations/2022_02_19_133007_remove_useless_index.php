<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class RemoveUselessIndex extends Migration
{
    private $idxShopId = "idx_shopid";

    /**
     *
     * dy-shandian    customize_orders    customize_orders_user_id_shop_id_index
     * dy-shandian    delivery_templates    delivery_templates_user_id_shop_id_index
     * dy-shandian    goods    goods_user_id_shop_id_index
     * dy-shandian    order_items    order_items_user_id_shop_id_created_at_index
     * dy-shandian    order_trace_list    order_trace_list_user_id_shop_id_status_index
     * dy-shandian    orders    orders_user_id_shop_id_pay_at_order_status_index
     * dy-shandian    packages    packages_user_id_shop_id_index
     * dy-shandian    query_areas    query_areas_user_id_shop_id_index
     * dy-shandian    query_templates    query_templates_user_id_shop_id_index
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table("orders", function (Blueprint $table) {
            $table->dropIndex("orders_user_id_shop_id_pay_at_order_status_index");
        });
        Schema::table("packages", function (Blueprint $table) {
            $table->dropIndex("packages_user_id_shop_id_index");
        });
        Schema::table("customize_orders", function (Blueprint $table) {
            $table->dropIndex("customize_orders_user_id_shop_id_index");
        });
        Schema::table("delivery_templates", function (Blueprint $table) {
            $table->dropIndex("delivery_templates_user_id_shop_id_index");
        });
        Schema::table("goods", function (Blueprint $table) {
            $table->dropIndex("goods_user_id_shop_id_index");
        });
        Schema::table("order_items", function (Blueprint $table) {
            $table->dropIndex("order_items_user_id_shop_id_created_at_index");
        });
        Schema::table("order_trace_list", function (Blueprint $table) {
            $table->dropIndex("order_trace_list_user_id_shop_id_status_index");
        });
        Schema::table("query_areas", function (Blueprint $table) {
            $table->dropIndex("query_areas_user_id_shop_id_index");
            $table->index(["shop_id"], $this->idxShopId);
        });
        Schema::table("query_templates", function (Blueprint $table) {
            $table->dropIndex("query_templates_user_id_shop_id_index");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table("query_areas", function (Blueprint $table) {

            $table->dropIndex($this->idxShopId);
        });
    }
}
