<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateActivityTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('activity', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name', 100)->comment('活动名');
            $table->tinyInteger('type')->default(0)->comment('类型 1续费活动 2平台活动');
            $table->string('image', 255)->comment('图片');
            $table->string('url', 255)->comment('跳转地址');
            $table->tinyInteger('status')->default(0)->comment('活动状态 0未开始 1进行中');

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('activity');
    }
}
