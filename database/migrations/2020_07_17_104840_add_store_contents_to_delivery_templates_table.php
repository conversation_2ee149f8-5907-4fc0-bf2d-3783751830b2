<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddStoreContentsToDeliveryTemplatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('delivery_templates', function (Blueprint $table) {
            $table->text('store_contents')->comment('备货单模板信息')->after('delivery_contents');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('delivery_templates', function (Blueprint $table) {
            $table->dropColumn('store_contents');
        });
    }
}
