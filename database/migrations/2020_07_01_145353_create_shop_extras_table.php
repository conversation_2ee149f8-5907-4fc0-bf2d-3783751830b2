<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateShopExtrasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shop_extras', function (Blueprint $table) {
            $table->increments('id');
	        $table->integer('user_id')->comment('用户id');
	        $table->integer('shop_id')->comment('shop_id');
	        $table->integer('index_combine_show')->default(1)->comment('1-单个 1-合并');
	        $table->string('warning_address_str', 255)->nullable()->comment('地址警示词语: ,分割');
	        $table->string('needle_items', 255)->nullable()->comment('需要的条件');
            $table->timestamps();
            $table->index(['user_id', 'shop_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shop_extras');
    }
}
