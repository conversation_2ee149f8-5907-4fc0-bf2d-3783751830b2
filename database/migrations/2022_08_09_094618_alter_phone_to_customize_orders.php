<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterPhoneToCustomizeOrders extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('customize_orders', function (Blueprint $table) {
            $table->string('receiver_phone', 20)->nullable()->comment('收货人手机')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('customize_orders', function (Blueprint $table) {
            $table->char('receiver_phone', 11)->nullable()->comment('收货人手机')->change();
        });
    }
}
