<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddIdxShopidOrderstatusPayat extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->index(['shop_id','order_status','pay_at','refund_status','send_at','locked_at','printed_at','receiver_phone'],'idx_shopid_orderstatus_payat_withdata');
            $table->index(['receiver_phone','shop_id','order_status','pay_at','refund_status','send_at','locked_at','printed_at'],'idx_rcphone_withdata');

            try{
                $table->dropIndex("orders_user_id_shop_id_created_at_index");
            }
            catch (Exception $ex){

            }

            try{
                $table->dropIndex("orders_user_id_shop_id_order_created_at_index");
            }catch (Exception $ex){

            }
        });




    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            //
        });
    }
}
