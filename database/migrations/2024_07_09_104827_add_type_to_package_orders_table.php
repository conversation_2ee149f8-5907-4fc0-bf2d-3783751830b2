<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTypeToPackageOrdersTable extends Migration
{
    public function up(): void
    {
        Schema::table('package_orders', function (Blueprint $table) {
            $table->tinyInteger('source_type')->default(0)->comment('来源类型 0:取号,1:内部发货');
        });
    }

    public function down(): void
    {
        Schema::table('package_orders', function (Blueprint $table) {
            //
        });
    }
}
