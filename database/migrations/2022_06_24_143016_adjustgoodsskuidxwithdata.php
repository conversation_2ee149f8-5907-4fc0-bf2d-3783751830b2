<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class Adjustgoodsskuidxwithdata extends Migration
{
    private $idxGoodsIdWithDataExt = 'idx_goodsid_withdata_ext';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('goods_skus', function (Blueprint $table) {

            $table->index(['goods_id', 'sku_value', 'sku_id', 'custom_sku_value', 'outer_id', 'deleted_at', 'updated_at'], $this->idxGoodsIdWithDataExt);
            $table->dropIndex("idx_goodsid_withdata");
        });



    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('goods_skus', function (Blueprint $table) {
            $table->dropIndex($this->idxGoodsIdWithDataExt);
        });
    }
}
