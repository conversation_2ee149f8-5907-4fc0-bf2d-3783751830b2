<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterCommoditiesShopid extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('commodity', function (Blueprint $table) {
            $table->bigInteger('shop_id')->comment('店铺ID');
        });
        Schema::table('commodity_sku', function (Blueprint $table) {
            $table->bigInteger('shop_id')->comment('店铺ID');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('commodity', function (Blueprint $table) {
            //
        });
    }
}
