<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateCustomGroupTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('custom_groups', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('shop_id')->default(0)->comment('店铺id');
            $table->string('name')->comment('分组名称');
            $table->string('value',64)->comment('分组值');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['shop_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('custom_groups');
    }
}
