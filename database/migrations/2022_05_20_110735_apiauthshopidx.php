<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class Apiauthshopidx extends Migration
{
    private $idxShopToken = "idx_shoptoken";

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('api_auth_shop', function (Blueprint $table) {

            $table->index(['shop_token'], $this->idxShopToken);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('api_auth_shop', function (Blueprint $table) {
            $table->dropIndex($this->idxShopToken);
        });
    }
}
