<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddWaybillWpIndexToPackagesTable extends Migration
{
    public function up(): void
    {
        Schema::table('packages', function (Blueprint $table) {
            $table->dropColumn('old_pt_logistics_id');
            $table->string('waybill_wp_index',64)->nullable()->comment('快递单快递公司索引');
        });
    }

    public function down(): void
    {
        Schema::table('packages', function (Blueprint $table) {
            //
        });
    }
}
