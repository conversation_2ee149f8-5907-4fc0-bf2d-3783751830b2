<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddOuterIidToOrderItemsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->string('outer_iid')->nullable()->comment('商家外部商品编码')->after('sku_value');
            $table->string('outer_sku_iid')->nullable()->comment('商家外部sku编码')->after('outer_iid');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->dropColumn('outer_iid');
            $table->dropColumn('outer_sku_iid');
        });
    }
}
