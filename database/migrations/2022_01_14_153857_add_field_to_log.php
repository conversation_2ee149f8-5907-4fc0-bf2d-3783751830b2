<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddFieldToLog extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('print_records', function (Blueprint $table) {
            $table->string('name_index', 100)->nullable()->comment('姓名搜索索引');
            $table->string('phone_index', 100)->nullable()->comment('手机号搜索索引');
        });

        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->string('name_index', 100)->nullable()->comment('姓名搜索索引');
            $table->string('phone_index', 100)->nullable()->comment('手机号搜索索引');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('print_records', function (Blueprint $table) {
            $table->dropColumn('name_index');
            $table->dropColumn('phone_index');
        });

        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->dropColumn('name_index');
            $table->dropColumn('phone_index');
        });
    }
}
