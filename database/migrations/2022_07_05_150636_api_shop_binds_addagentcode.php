<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class ApiShopBindsAddagentcode extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //
        Schema::table('api_shop_binds', function (Blueprint $table) {
            $table->string('agent_code')->nullable()->comment('代理Code');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('api_shop_binds', function (Blueprint $table) {
            $table->dropColumn('agent_code');
        });
    }
}
