<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddGoodsCustomTitle extends Migration
{
    private $idxShopIdCustomTitle = 'idx_shopid_customtitle';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('goods', function (Blueprint $table) {

            $table->index(['shop_id', 'custom_title'], $this->idxShopIdCustomTitle);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('goods', function (Blueprint $table) {
            $table->dropIndex($this->idxShopIdCustomTitle);
        });
    }
}
