<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterTableDeliveryTemplates20241120095557 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('delivery_templates', function (Blueprint $table) {
            //判断一下有没有beihuo_contents 这个字段，如果没有就添加
            if (!Schema::hasColumn('delivery_templates', 'beihuo_contents')) {
                $table->text('beihuo_contents')->nullable()->comment('备货内容');
            }
            if (!Schema::hasColumn('delivery_templates', 'fahuo_contents')) {
                $table->text('fahuo_contents')->nullable()->comment('对账单内容');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('delivery_templates', function (Blueprint $table) {
            //
        });
    }
}
