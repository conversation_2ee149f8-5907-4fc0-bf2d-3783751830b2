<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateWaybillHistoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('waybill_histories', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('店铺id');
            $table->integer('order_id')->comment('订单表id');
            $table->string('order_no', 200)->nullable()->comment('订单编号');
            $table->bigInteger('package_id')->default(0)->comment('包裹ID');
            $table->integer('template_id')->default(0)->comment('打印模板id');
            $table->string('parent_waybill_code', 255)->nullable()->comment('快运母单号');
            $table->string('waybill_code', 255)->nullable()->comment('电子面单号');
            $table->string('wp_code', 64)->nullable()->comment('物流公司编码');
            $table->tinyInteger('waybill_status')->default(0)->comment('回收状态 0=未回收，1=已回收');
            $table->text('print_data')->nullable()->comment('打印的加密数据');
            $table->timestamps();
            $table->comment = '面单获取记录表';
            $table->index(['user_id', 'shop_id']);
            $table->index('order_no');
            $table->index('waybill_code');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('waybill_histories');
    }
}
