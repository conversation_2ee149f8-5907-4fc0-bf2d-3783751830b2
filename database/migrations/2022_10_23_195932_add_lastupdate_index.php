<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddLastupdateIndex extends Migration
{
    private $idxShopIdStatusLastestUpdated = 'idx_shopid_status_latestupdatedat';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_trace_list', function (Blueprint $table) {

            $table->index(['shop_id', 'status', 'latest_updated_at'], $this->idxShopIdStatusLastestUpdated);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_trace_list', function (Blueprint $table) {
            $table->dropIndex($this->idxShopIdStatusLastestUpdated);
        });
    }
}
