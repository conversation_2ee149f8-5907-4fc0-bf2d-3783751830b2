<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddServiceIdAndSpecificationIdToShopsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
	    Schema::table('shops', function (Blueprint $table) {
		    $table->string('service_id', 100)->nullable()->comment('微信小店使用')->after('inviter');
		    $table->string('specification_id', 100)->nullable()->comment('微信小店使用')->after('service_id');
	    });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
	    Schema::table('shops', function (Blueprint $table) {
		    $table->dropColumn('service_id');
		    $table->dropColumn('specification_id');
	    });
    }
}
