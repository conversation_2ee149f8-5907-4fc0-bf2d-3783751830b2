<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddFactoryIdToOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->integer('factory_id')->default(0)->comment('厂家id')->after('shop_id');
            $table->string('custom_print_content')->default('')->comment('自定义打印内容')->after('district_code');
            $table->string('custom_group',64)->default('')->comment('自定义分组')->after('custom_print_content');

            $table->index(['factory_id', 'order_status', 'pay_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('factory_id');
            $table->dropColumn('custom_print_content');
            $table->dropColumn('custom_group');
        });
    }
}
