<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterShippingAddressesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('shipping_addresses', function (Blueprint $table) {
            $table->string('tel',50)->nullable()->comment('电话号码')->after('mobile')->change();
        });

        Schema::table('customize_orders', function (Blueprint $table) {
            $table->string('sender_tel', 50)->nullable()->comment('发货人电话')->after('sender_phone')->change();
            $table->string('receiver_tel', 50)->nullable()->comment('收货人电话')->after('receiver_phone')->change();
	    });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('shipping_addresses', function (Blueprint $table) {
            $table->string('tel',11)->nullable()->comment('电话号码')->after('mobile')->change();
        });

        Schema::table('customize_orders', function (Blueprint $table) {
            $table->string('sender_tel', 11)->nullable()->comment('发货人电话')->after('sender_phone')->change();
            $table->string('receiver_tel', 11)->nullable()->comment('收货人电话')->after('receiver_phone')->change();

	    });
    }
}
