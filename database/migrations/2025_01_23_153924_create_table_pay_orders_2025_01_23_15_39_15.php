<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateTablePayOrders20250123153915 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pay_orders', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('user_id')->unsigned();
            $table->string('order_no', 32)->unique()->comment('支付单号');
            $table->string('platform_order_no', 32)->nullable()->unique()->comment('平台支付单号');
            $table->bigInteger('business_order_id')->unsigned()->default(0)->comment('订单ID');
            $table->string('business_order_no', 32)->nullable()->comment('业务单号');
            $table->tinyInteger('business_type')->unsigned()->default(0)->comment('业务类型');
            $table->bigInteger('pay_setting_id')->unsigned()->default(0)->comment('支付设置表id');
            $table->string('appid', 32)->comment('应用ID');
            $table->tinyInteger('app_type')->unsigned()->comment('应用类型');
            $table->tinyInteger('trade_type')->unsigned()->comment('交易类型');
            $table->string('mchid', 32)->nullable()->comment('商户号');
            $table->string('openid', 128)->nullable()->comment('用户标识');
            $table->string('description', 255)->comment('商品描述');
            $table->decimal('order_amount', 8, 2)->comment('订单金额');
            $table->decimal('fee', 10, 2)->default('0.00')->comment('手续费');
            $table->decimal('actual_amount', 10, 2)->default('0.00')->comment('实际金额');
            $table->tinyInteger('order_status')->unsigned()->default(0)->comment('订单状态');
            $table->tinyInteger('refund_status')->unsigned()->default(0)->comment('退款状态');
            $table->decimal('refund_amount', 8, 2)->default('0.00')->comment('退款金额');
            $table->decimal('refundable_amount', 8, 2)->default('0.00')->comment('可退款金额');
            $table->string('attach', 255)->nullable()->comment('附加数据');
            $table->string('payer_client_ip', 255)->nullable()->comment('用户终端IP');
            $table->dateTime('time_expire_at')->nullable()->comment('交易结束时间');
            $table->dateTime('pay_at')->nullable()->comment('支付时间');
            $table->timestamps();
            $table->softDeletes();
            //
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('pay_orders', function (Blueprint $table) {
            //
        });
    }
}
