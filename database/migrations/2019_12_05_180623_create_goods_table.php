<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateGoodsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('goods', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('id');
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('店铺id');
            $table->tinyInteger('type')->default(0)->comment('订单类型');
            $table->string('num_iid', 64)->nullable()->comment('商品id');
            $table->string('outer_goods_id', 64)->nullable()->comment('第三方商品编码');
            $table->string('goods_title', 255)->nullable()->comment('商品名称');
            $table->string('custom_title', 255)->nullable()->comment('自定义商品名称');
            $table->string('goods_pic', 255)->nullable()->comment('商品主图链接');
            $table->tinyInteger('is_onsale')->default(0)->comment('上下架状态');
            $table->timestamp('goods_created_at')->nullable()->comment('平台创建时间');
            $table->timestamp('goods_updated_at')->nullable()->comment('平台更新时间');
            $table->timestamps();
            $table->softDeletes();

            $table->comment = '商品信息';
            $table->index(['user_id', 'shop_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('goods');
    }
}
