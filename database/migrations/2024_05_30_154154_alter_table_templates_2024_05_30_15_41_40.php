<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterTableTemplates20240530154140 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('templates', function (Blueprint $table) {
            $table->string('belong_shop_name',100)->nullable()->comment('所属店铺名称');
            $table->bigInteger('belong_shop_id')->nullable()->comment('所属店铺id');
        });
        //把owner_name作为belong_shop_name的默认值，shop_id作为belong_shop_id的默认值
        DB::table('templates')->whereNull('belong_shop_name')->whereNull('belong_shop_id')
            ->update(['belong_shop_name' => DB::raw('owner_name'),'belong_shop_id' => DB::raw('shop_id')]);


    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('templates', function (Blueprint $table) {

        });
    }
}
