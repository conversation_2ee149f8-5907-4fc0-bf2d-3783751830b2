<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddSourceToCompaniesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->tinyInteger('source')->default(0)->comment('是否虚拟分享电子面单 0=不，1=是')->after('status');
            $table->integer('source_userid')->nullable()->comment('授权分享面单用户id')->after('source');
            $table->tinyInteger('source_status')->default(0)->comment('授权分享面单的操作状态 0=正常，1=冻结')->after('source_userid');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->dropColumn('source');
            $table->dropColumn('source_userid');
            $table->dropColumn('source_status');
        });
    }
}
