<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddSourceShopId extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->integer('source_shopid')->nullable()->comment('授权分享面单店铺id')->after('source_userid');
        });

        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->integer('source_shopid')->nullable()->comment('授权分享面单店铺id')->after('source_userid');
            $table->index(['source_shopid', 'source']);
            $table->index(['source_shopid', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->dropColumn('source_shopid');
        });

        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->dropColumn('source_shopid');
            $table->dropIndex(['source_shopid', 'source']);
            $table->dropIndex(['source_shopid', 'created_at']);
        });
    }
}
