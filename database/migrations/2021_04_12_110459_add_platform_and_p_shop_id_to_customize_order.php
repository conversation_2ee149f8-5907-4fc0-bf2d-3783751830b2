<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddPlatformAndPShopIdToCustomizeOrder extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('customize_orders', function (Blueprint $table) {
            $table->integer('p_shop_id')->default(0)->comment('上级shop_id');
            $table->tinyInteger('platform')->default(0)->comment('订单来源');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('customize_orders', function (Blueprint $table) {
            $table->dropColumn('p_shop_id');
            $table->dropColumn('platform');
        });
    }
}
