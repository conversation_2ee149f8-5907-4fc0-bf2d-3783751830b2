<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateApiAuthShopTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('api_auth_shop', function (Blueprint $table) {
            $table->increments('id');
            $table->string('shop_identifier', 100)->nullable()->comment('店铺id唯一标识');
            $table->timestamp('shop_expires_at')->nullable()->comment('店铺订购过期时间');
            $table->string('shop_token', 100)->nullable()->comment('shop_token');
            $table->timestamp('token_expires_at')->nullable()->comment('shop_token过期时间');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('api_auth_shop');
    }
}
