<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSendWaybillTypeToPackagesTable extends Migration
{
    public function up(): void
    {
        Schema::table('packages', function (Blueprint $table) {
            // 发货单号类型 1:普通单号 2:多单号子号
            $table->tinyInteger('send_waybill_type')->default(1)->comment('发货单号类型 1:普通单号 2:一单多包主包裹 3:一单多包从包裹');
        });
    }

    public function down(): void
    {
        Schema::table('packages', function (Blueprint $table) {
            //
        });
    }
}
