<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddShopIdCreateAtIndexToCustomizeOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('customize_orders', function (Blueprint $table) {
            $table->index(['shop_id','print_status', 'created_at']);
            $table->index('receiver_phone');
            $table->index('receiver_name');
            $table->index('order_no');
            $table->index('waybill_code');
            $table->index('parent_waybill_code');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('customize_orders', function (Blueprint $table) {
            $table->dropIndex(['shop_id','print_status', 'created_at']);
            $table->dropIndex('receiver_phone');
            $table->dropIndex('receiver_name');
            $table->dropIndex('order_no');
            $table->dropIndex('waybill_code');
            $table->dropIndex('parent_waybill_code');
        });
    }
}
