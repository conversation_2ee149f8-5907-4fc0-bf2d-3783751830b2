<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShippingTemplatesTable extends Migration
{
    public function up(): void
    {
        Schema::create('shipping_templates', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('shop_id');
            $table->string('template_name');
            $table->text('delivery_contents');

            $table->timestamps();
            $table->index('shop_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('shipping_templates');
    }
}
