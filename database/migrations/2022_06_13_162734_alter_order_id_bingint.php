<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterOrderIdBingint extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('delivery_records', function (Blueprint $table) {
            $table->bigInteger('order_id')->change();
            $table->bigInteger('history_id')->change();
        });
        Schema::table('operation_logs', function (Blueprint $table) {
            $table->bigInteger('order_id')->change();
            $table->bigInteger('package_id')->change();
        });
        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->bigInteger('order_id')->change();
        });
        Schema::table('print_records', function (Blueprint $table) {
            $table->bigInteger('order_id')->change();
            $table->bigInteger('history_id')->change();
            $table->bigInteger('package_id')->change();
        });
        Schema::table('goods_skus', function (Blueprint $table) {
            $table->bigInteger('id')->change();
            $table->bigInteger('goods_id')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
