<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
	    Schema::table('orders', function (Blueprint $table) {
		    $table->dropColumn('merge_pid');
		    $table->dropColumn('is_merge');
		    $table->dropColumn('is_divide');
		    $table->dropColumn('divide_pid');
	    });
	    Schema::table('packages', function (Blueprint $table) {
		    $table->dropColumn('merge_pid');
		    $table->tinyInteger('auth_source')->default(0)->comment('面单平台')->after('template_id');
	    });
	    Schema::table('waybill_histories', function (Blueprint $table) {
		    $table->tinyInteger('auth_source')->default(0)->comment('面单平台')->after('template_id');
	    });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
	    Schema::table('orders', function (Blueprint $table) {
		    $table->integer('merge_pid')->default(0)->comment('地址合并父id');
		    $table->tinyInteger('is_merge')->default(0)->comment('是否合并订单');
		    $table->integer('is_divide')->default(0)->comment('是否拆单');
		    $table->integer('divide_pid')->default(0)->comment('拆弹之前的订单ID，取消合单用');
	    });

	    Schema::table('packages', function (Blueprint $table) {
		    $table->bigInteger('merge_pid')->default(0)->comment('合单主单ID');
		    $table->dropColumn('auth_source');
	    });
	    Schema::table('waybill_histories', function (Blueprint $table) {
		    $table->dropColumn('auth_source');
	    });
    }
}
