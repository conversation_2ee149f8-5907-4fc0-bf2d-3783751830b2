<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateCommodity extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('commodity', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->tinyInteger("type")->comment('1:平台商品,2货品');
            $table->string('name',256);
            $table->string('platform_out_id')->nullable()->comment('平台商品ID');
            $table->timestamps();

        });

        Schema::create('commodity_sku', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('commodity_id');
            $table->string('platform')->comment('平台');
            $table->string('platform_sku_out_id')->nullable()->comment('平台SkuId');
            $table->string('platform_commodity_out_id')->nullable()->comment('平台SkuId');
            $table->tinyInteger("type")->comment('1:平台商品SKU,2货品SKU');
            $table->string('name',256);
            $table->decimal('weight')->nullable()->comment('重量');
            $table->decimal('net_weight')->nullable()->comment('净重');
            $table->decimal('settlement_price')->nullbale()->comment('结算价格');
            $table->timestamps();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('commodity');
        Schema::dropIfExists('commodity_sku');
    }
}
