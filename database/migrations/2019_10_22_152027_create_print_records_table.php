<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreatePrintRecordsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('print_records', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('店铺id');
            $table->integer('order_id')->default(0)->comment('订单表id');
            $table->integer('history_id')->default(0)->comment('取号记录表id');
            $table->string('order_no', 255)->nullable()->comment('订单编号');
            $table->integer('package_id')->default(0)->comment('包裹ID');
            $table->string('parent_waybill_code', 255)->nullable()->comment('快运母单号');
            $table->string('waybill_code', 255)->nullable()->comment('电子面单号');
            $table->string('wp_code', 64)->nullable()->comment('物流公司编码');
            $table->string('receiver_province', 50)->nullable()->comment('收货人省份');
            $table->string('receiver_city', 50)->nullable()->comment('收货人城市');
            $table->string('receiver_district', 50)->nullable()->comment('收货人地区');
            $table->string('receiver_town', 50)->nullable()->comment('收货人街道');
            $table->string('receiver_name', 255)->nullable()->comment('收货人名字');
	        $table->string('receiver_phone', 255)->nullable()->comment('收货人手机');
            $table->integer('receiver_zip')->nullable()->comment('收件人邮编');
            $table->string('receiver_address', 255)->nullable()->comment('收货地址');
            $table->string('buyer_remark', 255)->nullable()->comment('买家留言');
            $table->timestamps();
            $table->softDeletes();
            $table->index(['user_id', 'shop_id']);
            $table->comment = '打印记录表';
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('print_records');
    }
}
