<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateCustomizeOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('customize_orders', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('授权记录 ID');
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('店铺id');
            $table->integer('template_id')->default(0)->comment('打印模板id');
            $table->string('order_no', 100)->nullable()->comment('订单编号');
            $table->tinyInteger('order_type')->default(0)->comment('订单类型');
            $table->string('production_type',100)->nullable()->comment('物品类型');
            $table->string('product_remark',200)->nullable()->comment('物品备注');
            $table->string('receiver_province', 50)->nullable()->comment('收货人省份');
            $table->string('receiver_city', 50)->nullable()->comment('收货人城市');
            $table->string('receiver_district', 50)->nullable()->comment('收货人地区');
            $table->string('receiver_town', 50)->nullable()->comment('收货人街道');
            $table->string('receiver_name', 50)->nullable()->comment('收货人名字');
            $table->char('receiver_phone', 11)->nullable()->comment('收货人手机');
            $table->integer('receiver_zip')->nullable()->comment('收件人邮编');
            $table->string('receiver_address', 255)->nullable()->comment('收货地址');
            $table->string('goods_info', 255)->nullable()->comment('商品信息');
            $table->string('num', 100)->nullable()->comment('数量');
            $table->text('seller_memo')->nullable()->comment('卖家备注');
            $table->string('parent_waybill_code', 255)->nullable()->comment('快运母单号');
            $table->string('waybill_code', 255)->nullable()->comment('电子面单号');
            $table->string('wp_code', 32)->nullable()->comment('物流公司编码');
            $table->tinyInteger('print_status')->default(0)->comment('打印状态');
            $table->timestamp('recycled_at')->nullable()->comment('回收时间');
            $table->timestamps();
            $table->softDeletes();
            $table->comment = '手动创建订单表';
            $table->index(['user_id', 'shop_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('customize_orders');
    }
}
