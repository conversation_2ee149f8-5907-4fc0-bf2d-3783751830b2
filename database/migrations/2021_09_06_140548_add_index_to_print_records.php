<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddIndexToPrintRecords extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('print_records', function (Blueprint $table) {
            $table->index('order_no');
            $table->index('parent_waybill_code');
            $table->index('waybill_code');
            $table->index('receiver_phone');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('print_records', function (Blueprint $table) {
            $table->dropIndex('order_no');
            $table->dropIndex('parent_waybill_code');
            $table->dropIndex('waybill_code');
            $table->dropIndex('receiver_phone');
        });
    }
}
