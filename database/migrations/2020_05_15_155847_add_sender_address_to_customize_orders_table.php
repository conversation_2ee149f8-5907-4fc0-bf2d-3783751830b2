<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddSenderAddressToCustomizeOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('customize_orders', function (Blueprint $table) {
            $table->string('sender_name', 50)->nullable()->comment('发货人名字')->after('receiver_address');
            $table->string('sender_phone', 11)->nullable()->comment('发货人手机')->after('sender_name');
            $table->char('sender_tel', 11)->nullable()->comment('发货人电话')->after('sender_phone');
            $table->string('sender_province', 50)->nullable()->comment('发货人省份')->after('sender_tel');
            $table->string('sender_city', 50)->nullable()->comment('发货人城市')->after('sender_province');
            $table->string('sender_district', 50)->nullable()->comment('发货人地区')->after('sender_city');
            $table->string('sender_detailaddress', 50)->nullable()->comment('发货人详细地址')->after('sender_district');
            $table->char('receiver_tel', 11)->nullable()->comment('收货人电话')->after('receiver_phone');
            $table->string('sender_addressinfo', 255)->nullable()->comment('发货地址')->after('sender_detailaddress');
            $table->string('goods_details', 255)->nullable()->comment('商品详细')->after('sender_addressinfo');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('customize_orders', function (Blueprint $table) {
            $table->dropColumn('sender_name');
            $table->dropColumn('sender_phone');
            $table->dropColumn('sender_tel');
            $table->dropColumn('sender_province');
            $table->dropColumn('sender_city');
            $table->dropColumn('sender_district');
            $table->dropColumn('sender_detailaddress');
            $table->dropColumn('receiver_tel');
            $table->dropColumn('sender_addressinfo');
            $table->dropColumn('goods_details');
        });
    }
}
