<?php
/**
 * Created by PhpStorm.
 * User: ${USER}
 * Date: 2022/2/17
 * Time: 19:27
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateApiShopBindsTable extends Migration
{
    public function up()
    {
        Schema::create('api_shop_binds', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('app_id',50)->comment('开放接口 app_id');
            $table->bigInteger('shop_id')->comment('店铺 ID');
            $table->timestamps();
            $table->softDeletes();

            $table->unique(['app_id','shop_id']);
            $table->index('shop_id');
        });
    }

    public function down()
    {
        Schema::dropIfExists('api_shop_binds');
    }
}
