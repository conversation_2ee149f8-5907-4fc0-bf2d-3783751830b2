<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterTableWaybillShareActions20250423113511 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('waybill_share_actions', function (Blueprint $table) {
            $table->tinyInteger('auth_source')->nullable()->comment('面单账号来源');
            $table->bigInteger('dest_shop_id')->nullable()->comment('目标店铺id,就是接受面单的店铺id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('waybill_share_actions', function (Blueprint $table) {
            //
        });
    }
}
