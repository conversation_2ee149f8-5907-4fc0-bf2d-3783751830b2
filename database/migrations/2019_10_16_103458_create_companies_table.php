<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateCompaniesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('ID');
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('店铺id');
            $table->tinyInteger('auth_source')->default(1)->comment('授权来源');
            $table->string('owner_id', 255)->nullable()->comment('owner_id');
            $table->string('owner_name', 255)->nullable()->comment('owner_name');
            $table->string('branch_name', 255)->nullable()->comment('网点名称');
            $table->string('branch_code', 255)->nullable()->comment('网点Code');
            $table->string('wp_code', 64)->comment('快递公司ID');
            $table->string('wp_name', 64)->comment('快递公司名称');
            $table->tinyInteger('status')->default(0)->comment('是否启用 0=不，1=是');
            $table->integer('quantity')->default(0)->comment('电子面单余额数量');
            $table->integer('cancel_quantity')->default(0)->comment('取消的面单总数');
            $table->integer('recycled_quantity')->default(0)->comment('已回收用面单数量');
            $table->integer('allocated_quantity')->default(0)->comment('已用面单数量');
            $table->text('templates')->comment('快递公司对于的模板信息json');
            $table->string('province')->nullable()->comment('省');
            $table->string('city')->nullable()->comment('市');
            $table->string('district')->nullable()->comment('区');
            $table->string('detail')->nullable()->comment('详细地址');
            $table->timestamps();
            $table->softDeletes();
            $table->index(['user_id', 'shop_id', 'auth_source']);
            $table->comment = '快递公司表';
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('companies');
    }
}
