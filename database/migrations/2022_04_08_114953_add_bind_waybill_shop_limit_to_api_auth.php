<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddBindWaybillShopLimitToApiAuth extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('api_auth', function (Blueprint $table) {
            $table->integer('bind_waybill_shop_limit')->default(5)->comment('绑定运单店铺限制');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('api_auth', function (Blueprint $table) {
            $table->dropColumn('bind_waybill_shop_limit');
        });
    }
}
