<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateApiAuthTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('api_auth', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name', 100)->nullable()->comment('机构名称');
            $table->string('desc', 255)->nullable()->comment('机构备注');
            $table->string('app_id', 50)->nullable()->comment('app_id');
            $table->string('app_key', 100)->nullable()->comment('app_key');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('api_auth');
    }
}
