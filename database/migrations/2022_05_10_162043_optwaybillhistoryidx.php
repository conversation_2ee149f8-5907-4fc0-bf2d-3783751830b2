<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class Optwaybillhistoryidx extends Migration
{
    private   $idx_createdat_shopid_waybillcode = 'idx_createdat_shopid_waybillcode';
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->index(['created_at','shop_id','waybill_code'], $this->idx_createdat_shopid_waybillcode);
        });
        try {
            Schema::table('waybill_histories', function (Blueprint $table) {
                $table->dropIndex("waybill_histories_created_at_index");
            });
        }catch (Exception $exception){

        }
        try {
            Schema::table('waybill_histories', function (Blueprint $table) {
                $table->dropIndex("waybill_histories_user_id_shop_id_index");
            });
        }catch (Exception $exception){

        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->dropIndex($this->idx_createdat_shopid_waybillcode);
        });
        //
    }
}
