<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateUserStatisticsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_statistics', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('id');
            $table->date('stat_at')->nullable()->comment('统计时间');
            $table->integer('interval')->default(1)->comment('间隔：1-天');
            $table->integer('count')->default(0)->comment('总用户数');
            $table->integer('free_count')->default(0)->comment('免费用户数');
            $table->integer('paid_count')->default(0)->comment('付费用户数');
            $table->integer('increase_count')->default(0)->comment('今日增长用户数');
            $table->integer('free_increase_count')->default(0)->comment('今日增长免费用户数');
            $table->integer('paid_increase_count')->default(0)->comment('今日增长付费用户数');
            $table->integer('expired_count')->default(0)->comment('今日过期用户数');
            $table->timestamps();
            $table->index(['stat_at', 'interval']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_statistics');
    }
}
