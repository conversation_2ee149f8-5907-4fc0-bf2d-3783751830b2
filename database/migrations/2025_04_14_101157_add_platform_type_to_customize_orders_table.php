<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddPlatformTypeToCustomizeOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('customize_orders', function (Blueprint $table) {
            $table->tinyInteger('platform_type')->nullable()->comment('平台类型')->after('order_type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('customize_orders', function (Blueprint $table) {
            $table->dropColumn('platform_type');
        });
    }
}
