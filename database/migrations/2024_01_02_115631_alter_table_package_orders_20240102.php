<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterTablePackageOrders20240102 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('package_orders', function (Blueprint $table) {
            $table->string('tid', 50)->nullable()->comment('主订单号');
            $table->string('oid', 50)->nullable()->comment('子订单号');
            $table->string('num_iid', 50)->nullable()->comment('商品编码');
            $table->string('sku_id', 50)->nullable()->comment('Sku编码');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
