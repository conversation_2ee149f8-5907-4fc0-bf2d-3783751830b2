<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterTableOrdersAddOrdercode extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //因为抖音闪电的数据库太大了，也用不上这个字段，就暂时跳过
        if (!\App\Utils\Environment::isDy()&&!\App\Utils\Environment::isTaoBao()) {
            Schema::table('orders', function (Blueprint $table) {
                //
                $table->string('order_code', 512)->nullable()->comment('订单代码，微信视频号用到');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            //
        });
    }
}
