<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CratePtLogisticsItemsTable extends Migration
{
    public function up(): void
    {
        Schema::create('pt_logistics_items', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('shop_id');
            $table->bigInteger('pt_logistics_id')->comment('物流id');
            $table->string('oid',50)->comment('子订单号');
            $table->bigInteger('order_id')->default(0)->comment('订单id');
            $table->bigInteger('order_item_id')->default(0)->comment('子订单id');
            $table->string('sku_id')->comment('sku id');
            $table->string('outer_sku_id')->comment('sku 编码');
            $table->string('num_iid')->comment('商品 id');
            $table->integer('num')->comment('发货数量');

            $table->timestamps();
            $table->softDeletes();

            $table->unique(['shop_id', 'pt_logistics_id', 'order_item_id']);
            $table->index('oid');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('pt_logistics_items');
    }
}
