<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddVerifyCodeTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //
        Schema::create('mobile_verify_codes', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('mobile', 11)->nullable(); 
            $table->string('code', 6)->nullable();           
            $table->string('code_expire', 20)->nullable();    
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
        Schema::dropIfExists('mobile_verify_codes');
    }
}
