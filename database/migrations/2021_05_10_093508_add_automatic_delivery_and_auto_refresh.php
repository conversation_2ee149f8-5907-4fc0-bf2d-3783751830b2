<?php
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
class AddAutomaticDeliveryAndAutoRefresh extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('shop_extras', function (Blueprint $table) {
            $table->tinyInteger('is_auto_refresh')->default(1)->comment('打印完是否自动刷新, 0不刷新 1刷新');
            $table->tinyInteger('is_automatic_delivery')->default(0)->comment('打印完是否自动发货, 0不发货 1发货');
        });
    }
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('shop_extras', function (Blueprint $table) {
            $table->dropIndex('is_auto_refresh');
            $table->dropIndex('is_automatic_delivery');
        });
    }
}