<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddOrderTypeToWaybillHistoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->tinyInteger('order_type')->default(1)->comment('订单类型 1普通订单 2自由打印 3代打订单');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->dropColumn('order_type');
        });
    }
}
