<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddIsHomeDeliveryToOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->tinyInteger('is_home_delivery')->default(0)->comment('是否送货上门');
            $table->tinyInteger('is_live_order')->default(0)->comment('是否直播订单');
            $table->tinyInteger('is_gift_order')->default(0)->comment('是否赠品订单');
            $table->timestamp('urge_shipment_at')->nullable()->comment('催发货时间');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            //
        });
    }
}
