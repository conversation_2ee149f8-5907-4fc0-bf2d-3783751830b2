<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsPreSalePromiseShipAtToOrderItemsTable extends Migration
{
    public function up(): void
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->tinyInteger('is_pre_sale')->default(0)->comment('是否预售');
            $table->timestamp('promise_ship_at')->nullable()->comment('承诺发货时间');
        });
    }

    public function down(): void
    {
        Schema::table('order_items', function (Blueprint $table) {
            //
        });
    }
}
