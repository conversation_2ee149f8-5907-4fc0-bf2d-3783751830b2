<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterPlatformOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
	    Schema::table('platform_orders', function (Blueprint $table) {
	    	$table->tinyInteger('status')->default(0)->comment('订单状态')->after('order_no');
	    	$table->tinyInteger('pay_type')->default(0)->comment('支付方式')->after('pay_at');
		    $table->string('sku_title', 128)->nullable()->comment('sku名称')->after('sku_id');
	    	$table->integer('duration')->default(0)->comment('1个月，3个月')->after('sku_spec');
	    	$table->tinyInteger('duration_unit')->default(0)->comment('时长单位, 0:天，1:月，2:年')->after('duration');
		    $table->string('identifier', 128)->nullable()->comment('唯一身份')->change();
	    });

	    Schema::table('user_extras', function (Blueprint $table) {
		    $table->string('identifier', 128)->nullable()->comment('店铺标识')->after('shop_id');
		    $table->string('version_desc', 128)->nullable()->comment('版本名称')->after('version');
		    $table->string('version', 128)->nullable()->comment('版本')->change();
	    });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
	    Schema::table('platform_orders', function (Blueprint $table) {
	    	$table->dropColumn('status');
	    	$table->dropColumn('pay_type');
	    	$table->dropColumn('sku_title');
	    	$table->dropColumn('duration');
	    	$table->dropColumn('duration_unit');
		    $table->integer('identifier')->comment('唯一标识')->change();
	    });

	    Schema::table('user_extras', function (Blueprint $table) {
		    $table->dropColumn('identifier');
		    $table->dropColumn('version_desc');
		    $table->integer('version')->comment('版本')->change();
	    });
    }
}
