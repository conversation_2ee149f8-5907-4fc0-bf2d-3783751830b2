<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddPrintrecordsSortidx extends Migration
{
    private $index = 'idx_shopid_batchno_printindex';
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('print_records', function (Blueprint $table) {

            $table->index(['shop_id','batch_no','print_index'], $this->index);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('print_records', function (Blueprint $table) {
            $table->dropIndex($this->index);
        });
    }
}
