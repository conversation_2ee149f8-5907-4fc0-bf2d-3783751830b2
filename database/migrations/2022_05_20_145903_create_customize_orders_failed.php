<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateCustomizeOrdersFailed extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('customize_orders_faileds', function (Blueprint $table) {
            $table->increments('id');
            $table->string('goods_name')->nullable()->comment('商品名称');
            $table->integer('goods_num')->comment('商品数量');
            $table->integer('shop_id')->comment('店铺id');
            $table->string('goods_memo')->nullable()->comment('备注');
            $table->string('receiver_name')->comment('收货人姓名');
            $table->string('receiver_phone')->comment('收货人手机号');
            $table->string('receiver_address')->comment('收货人地址');
            $table->tinyInteger('err_code')->comment('错误code 1：地址解析错误 2：手机号码格式错误');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('customize_orders_faileds');
    }
}
