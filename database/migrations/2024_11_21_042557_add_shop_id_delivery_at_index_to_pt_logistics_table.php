<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddShopIdDeliveryAtIndexToPtLogisticsTable extends Migration
{
    public function up(): void
    {
        Schema::table('pt_logistics', function (Blueprint $table) {
//            $table->dateTime('delivery_at')->nullable()->comment('发货时间')->change();
            DB::statement("ALTER TABLE pt_logistics MODIFY COLUMN delivery_at DATETIME DEFAULT NULL COMMENT '发货时间'");
            $table->index(['shop_id', 'delivery_at']);
        });
    }

    public function down(): void
    {
        Schema::table('pt_logistics', function (Blueprint $table) {
            //
        });
    }
}
