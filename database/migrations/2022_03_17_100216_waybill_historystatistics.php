<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class WaybillHistorystatistics extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('waybill_history_statistics', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('id');
            $table->integer('shop_id')->default(0)->comment('统计店铺id');
            $table->date('stat_at')->nullable()->comment('统计时间');
            $table->string('wp_code')->nullable()->comment('快递公司编码');
            $table->tinyInteger('auth_source')->default(0)->comment('快递所属平台');
            $table->integer('source_shopid')->default(0)->comment('统计店铺id');
            $table->integer('order_total_use')->default(0)->comment('使用面单总数');
            $table->integer('order_total_cancel')->default(0)->comment('取消面单总数');
            $table->integer('platform_order_total_use')->default(0)->comment('平台订单使用面单总数');
            $table->integer('platform_order_total_cancel')->default(0)->comment('平台订单取消总数');
            $table->integer('customize_order_total_use')->default(0)->comment('自由订单使用面单总数');
            $table->integer('customize_order_total_cancel')->default(0)->comment('自由订单取消总数');
            $table->timestamps();
            $table->index('stat_at');
            $table->index('source_shopid');
            $table->comment = '取号记录日结表';
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('waybill_history_statistics');
    }
}
