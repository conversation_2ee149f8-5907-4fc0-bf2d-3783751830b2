<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2025/4/21
 * Time: 15:14
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddOpenUserSourceToUsersTable extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('open_user_source', 100)->default('')->comment('开放平台用户来源')->after('open_token');
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            //
        });
    }
}
