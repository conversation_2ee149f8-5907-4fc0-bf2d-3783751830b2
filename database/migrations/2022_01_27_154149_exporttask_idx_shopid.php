<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class ExporttaskIdxShopid extends Migration
{
    private  $idxShopId = 'idx_shop_id';
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('export_task', function (Blueprint $table) {

            $table->index(['shop_id'], $this->idxShopId);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('export_task', function (Blueprint $table) {
            $table->dropIndex($this->idxShopId);
        });
    }
}
