<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddPrintDataItemsToWaybillHistories extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->text('print_data_items', 255)->nullable()->comment('打印商品信息')->after('extra');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->dropColumn('print_data_items');
        });
    }
}
