<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class Addgoodssearchidx extends Migration
{
    private $idxShopIdWithData = 'idx_shopid_withdata';
    private $idxGoodsIdWithData = 'idx_goodsid_withdata';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('goods', function (Blueprint $table) {
            $table->index(['shop_id', 'custom_title', 'num_iid', 'goods_title', 'outer_goods_id', 'deleted_at'], $this->idxShopIdWithData);
        });
        Schema::table('goods_skus', function (Blueprint $table) {
            $table->index(['goods_id', 'sku_value', 'sku_id', 'custom_sku_value',  'outer_id','deleted_at'], $this->idxGoodsIdWithData);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('goods', function (Blueprint $table) {
            $table->dropIndex($this->idxShopIdWithData);
        });
        Schema::table('goods_skus', function (Blueprint $table) {
            $table->dropIndex($this->idxGoodsIdWithData);
        });
    }
}
