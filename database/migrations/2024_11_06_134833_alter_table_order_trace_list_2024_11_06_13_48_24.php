<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterTableOrderTraceList20241106134824 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_trace_list', function (Blueprint $table) {
//            $table->dropIndex('idx_latestupdatedat_status_createdat');
            $table->index(['updated_at', 'status', 'created_at','latest_updated_at'],'idx_updatedat_status_createat_lastestupdatedat');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_trace_list', function (Blueprint $table) {
            //
        });
    }
}
