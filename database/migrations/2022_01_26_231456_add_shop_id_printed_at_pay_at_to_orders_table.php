<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddShopIdPrintedAtPayAtToOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->index(['shop_id', 'order_status', 'pay_at']);
            $table->index(['shop_id', 'send_at', 'pay_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex(['shop_id', 'order_status', 'pay_at']);
            $table->dropIndex(['shop_id', 'send_at', 'pay_at']);
        });
    }
}
