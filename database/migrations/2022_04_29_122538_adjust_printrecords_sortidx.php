<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AdjustPrintrecordsSortidx extends Migration
{
    private $index = 'idx_shopid_batchno_printindex';
    private $idxShopIdCreatedAtBatchNoPrintIndex='idx_shopid_createdat_batchno_printindex';
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('print_records', function (Blueprint $table) {

            $table->index(['shop_id','created_at','batch_no','print_index'], $this->idxShopIdCreatedAtBatchNoPrintIndex);
        });
        try {
            Schema::table('print_records', function (Blueprint $table) {
                $table->dropIndex($this->index);
            });
        }catch (Exception $ex){

        }


    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('print_records', function (Blueprint $table) {
            $table->dropIndex($this->idxShopIdCreatedAtBatchNoPrintIndex);
        });
    }
}
