<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AjdustOrdersReceiverphone extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        try {
            Schema::table('orders', function (Blueprint $table) {
                $table->dropIndex('orders_receiver_phone_index');
            });
        } catch (Exception $ex) {

        }
        Schema::table('orders', function (Blueprint $table) {
            $idxRcPhoneWithData = 'idx_rcphone_withdata';
            $table->dropIndex($idxRcPhoneWithData);
            $table->index(['receiver_phone', 'shop_id', 'order_status', 'pay_at', 'refund_status', 'send_at', 'locked_at', 'printed_at', 'district_code'], $idxRcPhoneWithData);
            $idxShopidOrderstatusPayatWithdata = 'idx_shopid_orderstatus_payat_withdata';
            $table->dropIndex($idxShopidOrderstatusPayatWithdata);
            $table->index(['shop_id', 'order_status', 'pay_at', 'refund_status', 'send_at', 'locked_at', 'printed_at', 'receiver_phone', 'district_code'], $idxShopidOrderstatusPayatWithdata);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            //
        });
    }
}
