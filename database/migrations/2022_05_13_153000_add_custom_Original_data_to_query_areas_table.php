<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddCustomOriginalDataToQueryAreasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('query_areas', function (Blueprint $table) {
            $table->mediumText('custom_original_data')->default('')->comment('自定义原始数据');
            $table->text('custom_district_str')->default('')->comment('自定义区域字符串');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('query_areas', function (Blueprint $table) {
            $table->dropColumn('custom_original_data');
            $table->dropColumn('custom_district_str');
        });
    }
}
