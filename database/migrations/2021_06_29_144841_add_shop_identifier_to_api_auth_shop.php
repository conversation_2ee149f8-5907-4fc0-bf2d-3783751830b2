<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddShopIdentifierToApiAuthShop extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('api_auth', function (Blueprint $table) {
            $table->string('shop_identifier', 100)->nullable()->comment('店铺唯一身份');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('api_auth', function (Blueprint $table) {
            $table->dropColumn('shop_identifier');
        });
    }
}
