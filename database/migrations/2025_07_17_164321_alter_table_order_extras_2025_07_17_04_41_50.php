<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterTableOrderExtras20250717044150 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_extras', function (Blueprint $table) {
            $table->tinyInteger('reissue_status')->nullable()->comment('淘宝补发状态 1:处理中（需补发） 2:处理成功 3:处理失败');
            $table->text('reissue_payload')->nullable()->comment('淘宝补发参数');
            $table->string('tid',50)->nullable()->comment('订单号');
            $table->index(['tid'],'idx_tid');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_extras', function (Blueprint $table) {
            //
        });
    }
}
