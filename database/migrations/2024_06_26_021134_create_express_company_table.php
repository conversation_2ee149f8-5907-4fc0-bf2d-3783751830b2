<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateExpressCompanyTable extends Migration
{
    public function up(): void
    {
        Schema::create('express_company', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name',30)->comment('快递公司名称');
            $table->string('wp_code',20)->comment('快递公司编码');
            $table->string('union_wp_code',20)->comment('快递公司统一编码');
            $table->tinyInteger('type')->comment('快递公司类型');
            $table->string('logo')->comment('快递公司logo');
            $table->text('templates')->comment('快递公司模板');

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('express_company');
    }
}
