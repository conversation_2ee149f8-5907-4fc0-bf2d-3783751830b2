<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDeliveryTypeToPackageOrdersTable extends Migration
{
    public function up(): void
    {
        Schema::table('package_orders', function (Blueprint $table) {
            $table->tinyInteger('delivery_type')->default(0)->comment('发货类型 0:无,1:首次发货,2:变更单号,3:补发,4:换货,99:其他');
        });
    }

    public function down(): void
    {
        Schema::table('package_orders', function (Blueprint $table) {
            //
        });
    }
}
