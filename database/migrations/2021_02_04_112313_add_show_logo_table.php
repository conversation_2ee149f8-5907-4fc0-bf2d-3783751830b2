<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddShowLogoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('templates', function (Blueprint $table) {
            $table->tinyInteger('show_logo')->default(0)->comment('快递公司logo, 0-不显示 1 显示')->after('waybill_type');
            $table->integer('horizontal')->default(0)->comment('水平偏移量')->after('show_logo');
            $table->integer('vertical')->default(0)->comment('垂直偏移量')->after('horizontal');  
        });
        
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('templates', function (Blueprint $table) {
            $table->dropColumn('show_logo');
            $table->dropColumn('horizontal');
            $table->dropColumn('vertical');
        });
    }
}
