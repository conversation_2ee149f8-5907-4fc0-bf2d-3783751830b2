<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddTemplateIdToQueryAreas extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('query_areas', function (Blueprint $table) {
            $table->integer('template_id')->default(0)->comment('打印模板id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('query_areas', function (Blueprint $table) {
            $table->dropColumn('template_id');
        });
    }
}
