<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateDistrictAssignsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('district_assigns', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('shop_id')->comment('店铺 ID');
            $table->tinyInteger('mode')->comment('模式');
            $table->string('district_name')->comment('地区名');
            $table->bigInteger('district_code')->comment('地区编码');
            $table->bigInteger('parent_code')->comment('父级编码');
            $table->tinyInteger('district_level')->comment('地区层级');
            $table->string('value')->default('')->comment('值');
            $table->tinyInteger('is_match')->default(1)->comment('是否匹配');
            $table->timestamps();
            $table->softDeletes();

            $table->unique(['shop_id', 'mode', 'district_code']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('district_assigns');
    }
}
