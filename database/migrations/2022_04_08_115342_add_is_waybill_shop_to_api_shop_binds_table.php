<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddIsWaybillShopToApiShopBindsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('api_shop_binds', function (Blueprint $table) {
            $table->tinyInteger('is_waybill_shop')->default(0)->comment('是否电子面单店铺')->after('shop_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('api_shop_binds', function (Blueprint $table) {
            $table->dropColumn('is_waybill_shop');
        });
    }
}
