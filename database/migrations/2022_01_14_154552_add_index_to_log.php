<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddIndexToLog extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('print_records', function (Blueprint $table) {
            $table->index('name_index');
            $table->index('phone_index');
        });

        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->index('name_index');
            $table->index('phone_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('print_records', function (Blueprint $table) {
            $table->dropIndex('name_index');
            $table->dropIndex('phone_index');
        });

        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->dropIndex('name_index');
            $table->dropIndex('phone_index');
        });
    }
}
