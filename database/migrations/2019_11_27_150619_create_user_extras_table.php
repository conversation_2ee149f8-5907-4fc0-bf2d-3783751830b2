<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateUserExtrasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_extras', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('ID');
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('店铺id');
            $table->tinyInteger('version')->default(0)->comment('版本号');
            $table->timestamp('buy_at')->nullable()->comment('授权时间');
            $table->timestamp('expire_at')->nullable()->comment('到期时间');
            $table->integer('expire_days')->comment('过期时间');
            $table->integer('pay_amount')->default(0)->comment('支付金额');
            $table->timestamps();
            $table->softDeletes();
            $table->comment = '用户版本表';
            $table->index(['user_id', 'shop_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_extras');
    }
}
