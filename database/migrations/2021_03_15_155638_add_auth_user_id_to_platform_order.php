<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddAuthUserIdToPlatformOrder extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('platform_orders', function (Blueprint $table) {
            $table->string('auth_user_id', 128)->nullable()->comment('用户openId');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('platform_orders', function (Blueprint $table) {
            $table->dropColumn('auth_user_id');
        });
    }
}
