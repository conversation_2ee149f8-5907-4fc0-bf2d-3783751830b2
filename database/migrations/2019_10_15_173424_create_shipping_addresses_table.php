<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateShippingAddressesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shipping_addresses', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('ID');
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('店铺id');
            $table->string('sender_name', 128)->comment('寄件人');
            $table->string('mobile', 11)->comment('手机号码');
            $table->string('postal_code', 32)->comment('邮编编码');
            $table->string('province', 32)->comment('省');
            $table->string('city', 32)->comment('市');
            $table->string('district', 32)->comment('区');
            $table->string('address', 32)->comment('详细地址');
            $table->tinyInteger('is_default')->default(0)->comment('默认地址 0=不是，1=是');
            $table->timestamps();
            $table->softDeletes();
            $table->index(['user_id', 'shop_id']);
            $table->comment = '发货地址表';
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shipping_addresses');
    }
}
