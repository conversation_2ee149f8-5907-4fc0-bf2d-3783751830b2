<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddTemplateIdToPrintRecordsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('print_records', function (Blueprint $table) {
            $table->integer('template_id')->default(0)->comment('模板id');
            $table->string('template_name',30)->nullable()->comment('模板名');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('print_records', function (Blueprint $table) {
            //
        });
    }
}
