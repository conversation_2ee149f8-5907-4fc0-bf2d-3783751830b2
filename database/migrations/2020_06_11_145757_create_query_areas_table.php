<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateQueryAreasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('query_areas', function (Blueprint $table) {
            $table->increments('id');
	        $table->integer('user_id')->comment('用户id');
	        $table->integer('shop_id')->comment('用户id');
	        $table->string('name')->comment('名字');
	        $table->text('province_str')->nullable()->comment('省');
	        $table->text('city_str')->nullable()->comment('市');
	        $table->text('district_str')->nullable()->comment('区');
	        $table->mediumText('data')->comment('数据');
	        $table->timestamps();
	        $table->softDeletes();
	        $table->index(['user_id', 'shop_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('query_areas');
    }
}
