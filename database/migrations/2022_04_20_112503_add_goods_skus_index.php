<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddGoodsSkusIndex extends Migration
{
    private $idxGoodsId = 'idx_goodsid';
    private $idxSkuId = 'idx_skuid';
    private $idxOuterId = 'idx_outerid';
    private $idxOuterGoodsId = 'idx_outergoodsid';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        try {
            Schema::table('goods_skus', function (Blueprint $table) {
                $table->dropIndex('goods_skus_goods_id_index');
            });
        } catch (Exception $ex) {

        }
        try {
            Schema::table('goods_skus', function (Blueprint $table) {
                $table->dropIndex('goods_skus_sku_id_index');
            });
        } catch (Exception $ex) {

        }
        Schema::table('goods_skus', function (Blueprint $table) {

            $table->index(['goods_id'], $this->idxGoodsId);
            $table->index(['sku_id'], $this->idxSkuId);
            $table->index(['outer_id'], $this->idxOuterId);
            $table->index(['outer_goods_id'], $this->idxOuterGoodsId);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('goods_skus', function (Blueprint $table) {
            $table->dropIndex($this->idxGoodsId);
            $table->dropIndex($this->idxSkuId);
            $table->dropIndex($this->idxOuterId);
            $table->dropIndex($this->idxOuterGoodsId);
        });
    }
}
