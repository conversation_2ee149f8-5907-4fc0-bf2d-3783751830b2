<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddGoodsTitleToOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->string('goods_title')->nullable()->comment('商品标题')->after('village_flag');
            $table->string('goods_title_last')->nullable()->comment('商品标题')->after('goods_title');
            $table->string('sku_value')->nullable()->comment('SKU的值')->after('goods_title_last');
            $table->string('sku_value_last')->nullable()->comment('SKU的值')->after('sku_value');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('goods_title');
            $table->dropColumn('goods_title_last');
            $table->dropColumn('sku_value');
            $table->dropColumn('sku_value_last');
        });
    }
}
