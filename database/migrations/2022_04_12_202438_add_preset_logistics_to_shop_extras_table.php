<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddPresetLogisticsToShopExtrasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('shop_extras', function (Blueprint $table) {
            $table->string('preset_logistics_union_wp_code')->default('')->comment('预设快递公司编码');
            $table->tinyInteger('preset_logistics_district_switch')->default(0)->comment('根据区域预设快递开关');
            $table->json('preset_logistics_district_data')->comment('根据区域预设快递数据');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('shop_extras', function (Blueprint $table) {
            $table->dropColumn('preset_logistics_union_wp_code');
            $table->dropColumn('preset_logistics_district_switch');
            $table->dropColumn('preset_logistics_district_data');
        });
    }
}
