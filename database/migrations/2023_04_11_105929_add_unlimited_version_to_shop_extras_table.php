<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddUnlimitedVersionToShopExtrasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('shop_extras', function (Blueprint $table) {
            $table->tinyInteger('is_unlimited_version')->default(0)->comment('不限制版本');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('shop_extras', function (Blueprint $table) {
            $table->dropColumn('is_unlimited_version');
        });
    }
}
