<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddQualityDeliveryStatusToOrderItemExtrasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_item_extras', function (Blueprint $table) {
            $table->tinyInteger('quality_delivery_status')->default(0)->comment('质检发货状态');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_item_extras', function (Blueprint $table) {
            $table->dropColumn('quality_delivery_status');
        });
    }
}
