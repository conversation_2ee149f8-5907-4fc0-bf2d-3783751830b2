<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterTableOrderCipherInfo20240308094101 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_cipher_info', function (Blueprint $table) {
            $table->text('receiver_telephone_ciphertext')->default('')->comment('电话号码密文')->after('receiver_address_mask');
            $table->text('receiver_telephone_mask')->default('')->comment('电话号码脱敏')->after('receiver_telephone_ciphertext');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_cipher_info', function (Blueprint $table) {
            //
        });
    }
}
