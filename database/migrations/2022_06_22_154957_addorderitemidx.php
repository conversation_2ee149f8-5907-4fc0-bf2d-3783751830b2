<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class Addorderitemidx extends Migration
{
    private $idxOuterIid = 'idx_outeriid';
    private $idxOuterSkuIid='idx_outeskuiid';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->index(['outer_iid'], $this->idxOuterIid);
            $table->index(['outer_sku_iid'], $this->idxOuterSkuIid);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->dropIndex($this->idxOuterIid);
            $table->dropIndex($this->idxOuterSkuIid);
        });
    }
}
