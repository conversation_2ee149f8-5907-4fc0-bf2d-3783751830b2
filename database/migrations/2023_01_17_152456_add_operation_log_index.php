<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddOperationLogIndex extends Migration
{
    private $idxShopIdTypeTime = 'idx_shopid_type_time';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('operation_logs', function (Blueprint $table) {
            $table->index(['shop_id', 'type', 'time'], $this->idxShopIdTypeTime);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('operation_logs', function (Blueprint $table) {
            $table->dropIndex($this->idxShopIdTypeTime);
        });
    }
}
