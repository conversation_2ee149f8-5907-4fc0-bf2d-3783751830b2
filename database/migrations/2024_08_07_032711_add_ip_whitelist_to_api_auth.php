<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIpWhitelistToApiAuth extends Migration
{
    public function up(): void
    {
        Schema::table('api_auth', function (Blueprint $table) {
            $table->string('ip_whitelist')->default('')->comment('ip白名单');
            $table->integer('request_freq_limit')->default(0)->comment('请求速率限制(0不限制)');

        });
    }

    public function down(): void
    {
        Schema::table('api_auth', function (Blueprint $table) {
            //
        });
    }
}
