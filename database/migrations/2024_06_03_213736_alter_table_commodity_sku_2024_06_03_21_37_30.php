<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterTableCommoditySku20240603213730 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('commodity_sku', function (Blueprint $table) {
            $table->decimal('settlement_price', 10, 2)->nullable()->comment('结算价')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('commodity_sku', function (Blueprint $table) {
            //
        });
    }
}
