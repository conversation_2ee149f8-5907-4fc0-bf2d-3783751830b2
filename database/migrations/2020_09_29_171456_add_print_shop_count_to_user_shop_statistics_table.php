<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddPrintShopCountToUserShopStatisticsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
	    Schema::table('user_shop_statistics', function (Blueprint $table) {
		    $table->integer('print_shop_count')->default(0)->comment('打印店铺总数')->after('template_count');
	    });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
	    Schema::table('user_shop_statistics', function (Blueprint $table) {
		    $table->dropColumn('print_shop_count');
	    });
    }
}
