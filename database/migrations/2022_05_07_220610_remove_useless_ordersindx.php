<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class RemoveUselessOrdersindx extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        try {
            Schema::table('orders', function (Blueprint $table) {
                $table->dropIndex("idx_rcphone_withdata");
            });
        }catch (Exception $exception){

        }
        try {
            Schema::table('orders', function (Blueprint $table) {
                $table->dropIndex("address_md5_index");
            });
        }catch (Exception $exception){

        }
        try {
            Schema::table('orders', function (Blueprint $table) {
                $table->dropIndex("idx_shopid_rcphone_payat_withdata");
            });
        }catch (Exception $exception){

        }
        try {
            Schema::table('waybill_histories', function (Blueprint $table) {
                $table->dropIndex("waybill_histories_source_userid_created_at_index");
            });
        }catch (Exception $exception){

        }



    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
