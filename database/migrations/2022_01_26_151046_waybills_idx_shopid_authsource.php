<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class WaybillsIdxShopidAuthsource extends Migration
{
    private $idexSourceIdAuthSource = 'idx_shopid_authsource';
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('waybills', function (Blueprint $table) {
            //
            $table->index(['shop_id','auth_source'], $this->idexSourceIdAuthSource);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('waybills', function (Blueprint $table) {
            $table->dropIndex($this->idexSourceIdAuthSource);
        });
    }
}
