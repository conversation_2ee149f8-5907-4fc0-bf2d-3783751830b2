<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAftersaleOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('aftersale_order', function (Blueprint $table) {
            $table->increments('id');
            $table->string('tid', 100)->nullable()->comment('退款id');
            $table->integer('shop_id')->nullable()->comment('店铺id');
            $table->string('refund_id', 100)->nullable()->comment('退款id');
            $table->tinyInteger('refund_status')->comment('退款状态');
            $table->string('refund_reason')->nullable()->comment('退款原因');
            $table->string('refund_memo')->nullable()->comment('商家备注');
            $table->decimal('refund_price', 10, 2)->default(0.00)->comment('退款金额');
            $table->timestamp('refund_created_at')->nullable()->comment('申请时间');
            $table->timestamp('refund_updated_at')->nullable()->comment('修改时间');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('aftersale_order');
    }
}
