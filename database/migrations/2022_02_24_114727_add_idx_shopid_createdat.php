<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddIdxShopidCreatedat extends Migration
{
    private $idx_shopid_createdat = 'idx_shopid_createdat';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {

            $table->index(['shop_id', 'created_at'], $this->idx_shopid_createdat);


        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex($this->idx_shopid_createdat);
        });
    }
}
