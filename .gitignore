# Created by .ignore support plugin (hsz.mobi)
.idea/
#composer.lock
/vendor/
*.iml
*.code-workspace
dump.rdb
*.bak
.env
.env_bak
.env-production
.cursorignore
_ide_helper.php
Jenkinsfile
#/public/
#/storage/
.env_example
npm-debug.log
.DS_Store
/storage/framework/laravel-excel/*
/storage/framework/cache/*
/storage/framework/views/*
/storage/app/export/*
/storage/logs/*
.cursor
.lingma


# 放最后
!**/.gitkeep
/composer.phar
