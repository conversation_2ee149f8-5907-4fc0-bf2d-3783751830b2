services:
    php:
        build:
            context: .
            dockerfile: Dockerfile
        image: print-back-php:latest
        entrypoint: ["sh","-c","cp /tmp/docker/supervisord/main_nginx_fpm.conf /app/conf/supervisor.conf && /app/entrypoint.sh"]
        container_name: print-back-php
        platform: linux/amd64
        working_dir: ${CODE_PATH}
        ports:
            - 8088:8080
        environment:
            SERVER_PORT: 8080
            CODE_PATH: ${CODE_PATH}
        stdin_open: true
        tty: true
        volumes:
            - ../../:${CODE_PATH}
        extra_hosts:
            - host.docker.internal:host-gateway
