server {
	listen 80;

	server_name localhost;
	root /var/www/code/public;

	# security
	include common/security.conf;

	# logging
	access_log /var/log/nginx/default_sucess.log main;
	error_log /var/log/nginx/default_error.log;

	index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        #include conf.d/inc/proxy.conf;
        include common/cors.conf;
        include common/php_fastcgi.conf;
    }

	# additional config
	include common/general.conf;
}
