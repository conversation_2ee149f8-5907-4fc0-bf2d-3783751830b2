# do daily/weekly/monthly maintenance
# min   hour    day     month   weekday command
# *     * 		* 		*	 	* 		/usr/bin/php /usr/share/nginx/html/artisan schedule:run >> /dev/null 2>&1
# Run every minute
# */1 * * * * echo "hello"
# Run every 2 seconds see https://github.com/aptible/supercronic/tree/master/cronexpr
# */2 * * * * * * ls 2>/dev/null

* * * * * /usr/bin/php /app/code/artisan schedule:run >> /dev/null 2>&1
