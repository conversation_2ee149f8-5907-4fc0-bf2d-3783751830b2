RUN yum install -y --enablerepo=updates \
        epel-release \
        which \
        curl \
        curl-devel \
        make  \
        wget \
        gcc  \
        gcc-c++  \
        gettext \
        net-tools \
        python-setuptools \
        openssl \
        openssl-devel \
        bzip2-devel \
        zlib \
        zlib-devel \
        zip \
        unzip \
        yum-utils \
        libpng \
        libjpeg \
        libpng-devel \
        libjpeg-devel \
        ghostscript \
        libtiff \
        libtiff-devel \
        freetype \
        freetype-devel \
   && yum install -y http://rpms.remirepo.net/enterprise/remi-release-7.rpm \
   && yum-config-manager --enable remi-php73 \
   && yum install -y php php-devel php-fpm php-cli php-bcmath php-gd php-json php-mbstring php-mcrypt \
            php-mysqlnd php-opcache php-process php-sodium php-pdo php-pecl-crypto php-pecl-mcrypt php-pecl-geoip php-pecl-swoole \
            php-recode php-snmp php-soap php-xmll php-calendar php-core php-ctype php-curl php-date php-dom \
            php-exif php-fileinfo php-filter php-ftp php-gettext php-hash php-iconv php-intl php-ldap php-libxml \
            php-pecl-memcached php-pecl-redis php-mysqli php-mysqlnd php-openssl php-pcntl php-pcre php-pdo \
            php-pdo_mysql php-pdo_sqlite php-phar php-posix php-reflection php-session php-simpleXML php-spl \
            php-sqlite3 php-standard php-tokenizer php-xml php-zip php-zlib php-xmlreader php-xmlwriter php-yac php-pecl-gmagick \
   && mkdir -p /app/conf \
   && cd /tmp \
   && tar zxvf nginx-1.14.0.tar.gz \
   && cd nginx-1.14.0 \
   && ./configure --prefix=/usr/local/nginx --user=apps --group=apps --with-http_stub_status_module \
   && make \
   && make install \
   && ln -s /usr/local/nginx/sbin/* /usr/local/sbin/ \
   && cd /tmp \
   && tar zxvf GraphicsMagick-1.3.32.tar.gz \
   && cd GraphicsMagick-1.3.32 \
   && ./configure --prefix=/usr/local/GraphicsMagick \
   && make \
   && make install \
   && cd /tmp \
   && tar zxvf pngquant-2.8.2-src.tar.gz \
   && cd pngquant-2.8.2 \
   && ./configure \
   && make && make install \
   && easy_install -i http://pypi.pdd.net/repository/pypi-public/simple supervisor \
   && mkdir -p /app \
   && mkdir -p /usr/local/supervisor/conf.d \
   && chown -R apps:apps /app /usr/local/supervisor/conf.d /usr/local/nginx /var/log/php-fpm \
   && chmod +x /tmp/start.sh \
   && chmod +x /tini \
   && chmod 777 /var/run \
   && chmod 777 /var/log \
   && mkfifo /var/log/stdout \
   && chmod 777 /var/log/stdout \
   # install ImageMagick#
   && cd /tmp \
   && tar zxvf ImageMagick.tar.gz \
   && cd ImageMagick-7.0.8-60  \
   && ./configure --prefix=/usr/local/imagemagick --enable-share --enable-static LDFLAGS="-L/usr/lib64" CPPFLAGS="-I/usr/include" \
   && make && make install \
   && cd /usr/local/imagemagick \
   && ldconfig /usr/local/lib \
   && cd /tmp \
   && tar zxvf imagick-3.4.4.tgz \
   && cd imagick-3.4.4 \
   && /usr/bin/phpize \
   && ./configure --with-php-config=/usr/bin/php-config --with-imagick=/usr/local/imagemagick \
   && make && make install

COPY supervisord.conf /etc/
COPY main.conf /usr/local/supervisor/conf.d/
COPY php.ini www.conf php-fpm.conf /app/conf/
COPY nginx.template /usr/local/nginx/conf/

RUN ln -sf /app/conf/php.ini /etc/php.ini \
   && ln -sf /app/conf/php-fpm.conf /etc/php-fpm.conf \
   && ln -sf /app/conf/www.conf /etc/php-fpm.d/www.conf