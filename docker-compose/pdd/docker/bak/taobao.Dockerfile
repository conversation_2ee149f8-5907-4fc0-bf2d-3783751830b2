FROM php:7.2-fpm-alpine

# Environments
ENV TIMEZONE            Asia/Shanghai
#ENV PHP_MEMORY_LIMIT    256M
#ENV MAX_UPLOAD          50M
#ENV PHP_MAX_FILE_UPLOAD 200
#ENV PHP_MAX_POST        100M
#ENV COMPOSER_ALLOW_SUPERUSER 1
ENV CODE_PATH /var/www/code
ENV PHP_ENV_FILE pro_tb.env

#安装基本工具
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories \
    && apk update \
    && apk add --no-cache \
        nginx \
        openssh \
        supervisor \
        git \
        curl \
        curl-dev \
        zlib-dev \
        build-base \
        vim \
#        wget \
        imagemagick \
        imagemagick-dev \
        libtool \
        libxml2-dev \
        autoconf \
        libmcrypt-dev \
        freetype-dev \
        libjpeg-turbo-dev \
        shadow \
        libpng-dev \
        tzdata \
        sudo
RUN pecl install mcrypt \
    && pecl install yac \
    && pecl install -o -f redis \
    && docker-php-ext-install \
        curl \
        mbstring \
        pdo \
        pcntl \
        tokenizer \
        xml \
        zip \
        pdo_mysql \
        opcache \
        mysqli \
        sockets \
    && docker-php-ext-enable redis \
    && docker-php-ext-enable opcache \
    && docker-php-ext-enable mcrypt \
    && docker-php-ext-enable yac \
    && docker-php-ext-install -j"$(getconf _NPROCESSORS_ONLN)" iconv \
    && docker-php-ext-configure gd --with-freetype-dir=/usr/include/ --with-jpeg-dir=/usr/include/ \
    && docker-php-ext-install -j"$(getconf _NPROCESSORS_ONLN)" gd \
    && rm -rf /tmp/pear \
    && rm -rf /var/cache/apk/* \
    && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo $TIMEZONE > /etc/timezone
#  enable sockets, enable openssl , enable http2,enable mysqlnd , enable json , enable curl
RUN printf "yes\nyes\nyes\nno\nyes\nyes\n" | pecl install http://pecl.php.net/get/swoole-4.6.3.tgz \
    && docker-php-ext-enable swoole

WORKDIR $CODE_PATH

#6.ADD-CRONTABS
COPY docker/crontabs/default /var/spool/cron/crontabs/
RUN cat /var/spool/cron/crontabs/default >> /var/spool/cron/crontabs/root \
    && mkdir -p /var/log/cron \
    && touch /var/log/cron/cron.log

ENV php_vars /usr/local/etc/php/conf.d/docker-vars.ini
# tweak php-fpm config
RUN echo "upload_max_filesize = 50M"  >> ${php_vars} &&\
    echo "post_max_size = 50M"  >> ${php_vars} &&\
    echo "variables_order = \"EGPCS\""  >> ${php_vars} && \
    echo "memory_limit = 128M"  >> ${php_vars}


#COPY docker/composer-setup.php /var/www/code
# install composer
#RUN  php composer-setup.php \
#    && php -r "unlink('composer-setup.php');" \
#    && mv composer.phar /usr/local/bin/composer \
#    && composer config -g repo.packagist composer https://mirrors.aliyun.com/composer/

# 先拉取 composer 利用缓存机制，减少重复构建
#COPY composer.* ./
#COPY app/Helper/functions.php ./app/Helper/functions.php
#RUN composer install --no-dev -o

#COPY docker/php/www.conf /usr/local/etc/php-fpm.d/
COPY docker/supervisord/conf.d/* /etc/supervisor/conf.d/
#ADD docker/supervisord/supervisord.conf /etc/supervisord.conf
COPY docker/nginx/ /etc/nginx/
COPY docker/entrypoint.sh /var/www/
#COPY . ./
RUN sed -i "s#/etc/supervisor.d/\*.ini#/etc/supervisor/conf.d/\*.conf#" /etc/supervisord.conf \
    && chmod +x /var/www/entrypoint.sh


ENTRYPOINT ["/var/www/entrypoint.sh"]
CMD ["start"]
EXPOSE 80

