[program:cron]
command=/bin/bash -c 'supercronic /app/conf/cron.conf'
autostart=true
autorestart=true
stdout_logfile=/var/log/supervisor/cron.log
stopsignal=QUIT

[program:nginx]
command=/bin/bash -c 'envsubst "\$SERVER_PORT \$CODE_PATH"  < /usr/local/nginx/conf/nginx.template > /usr/local/nginx/conf/nginx.conf && /usr/local/sbin/nginx -g "daemon off;"'
stopsignal=QUIT

[program:php-fpm]
command=/bin/bash -c '/usr/sbin/php-fpm -F -O'
autostart=true
autorestart=true
priority=5
stdout_logfile=/var/log/supervisor/php-fpm.log
stopsignal=QUIT

[program:queue]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php $CODE_PATH/artisan queue:work redis --queue=high,default --tries=2 --timeout=600
autostart=true
autorestart=true
numprocs=3
redirect_stderr=true
stdout_logfile=/var/log/supervisor/queue.log

[program:queue-swoole]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php $CODE_PATH/artisan queue:work redis --queue=swoole --tries=2
autostart=true
autorestart=true
numprocs=1
redirect_stderr=true
stdout_logfile=/var/log/supervisor/queue-swoole.log

# 初始化 env
[program:init]
command=/bin/bash -c 'cp -f /app/$PHP_ENV_FILE /app/.env && tailf /app/.env'
autostart=true
autorestart=true
priority=2
stdout_logfile=/var/log/supervisor/init.log
stopsignal=QUIT
