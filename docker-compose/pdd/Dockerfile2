FROM centos:7.5.1804

RUN sed -e 's|^mirrorlist=|#mirrorlist=|g' \
           -e 's|^#baseurl=http://mirror.centos.org/centos|baseurl=https://mirrors.huaweicloud.com/centos|g' \
           -i.bak \
           /etc/yum.repos.d/CentOS-*.repo \
    && yum install -y wget  \
        epel-release \
    && mv /etc/yum.repos.d/CentOS-Base.repo /etc/yum.repos.d/CentOS-Base.repo.backup \
    && wget -O /etc/yum.repos.d/CentOS-Base.repo https://mirrors.aliyun.com/repo/Centos-7.repo \
    && mv /etc/yum.repos.d/epel.repo /etc/yum.repos.d/epel.repo.backup \
    && mv /etc/yum.repos.d/epel-testing.repo /etc/yum.repos.d/epel-testing.repo.backup \
    && wget -O /etc/yum.repos.d/epel.repo https://mirrors.aliyun.com/repo/epel-7.repo \
    && yum clean all \
    && rm -rf /var/cache/yum \
    && yum makecache \
    && groupadd apps \
    && useradd -g apps apps \
    && yum install -y supervisor

# pdd 分界线

#COPY docker /tmp/docker/
ADD docker.tar.gz /tmp/

RUN mkdir -p /app/conf \
    # uninstall swoole \
    && pecl uninstall swoole \
    # install swoole \
    && pecl install -D 'enable-sockets="no" enable-openssl="yes" enable-http2="yes" enable-mysqlnd="no" enable-swoole-json="yes" enable-swoole-curl="yes"' /tmp/docker/software/swoole-4.6.7.tgz \
    && echo "extension=swoole.so" > /etc/php.d/40-swoole.ini \
    && cd /tmp/docker/software \
    # cron \
    && cd /tmp/docker/software/ \
    && mv supercronic-linux-amd64-v0.2.30 supercronic \
    && chmod +x supercronic \
    && mv supercronic /usr/bin/supercronic \
    # config
    && cp /tmp/docker/crontabs/default /app/conf/cron.conf \
    && php -m

# pdd 分界线

RUN wget https://mirrors.tencent.com/composer/composer.phar \
    && mkdir -p /usr/local/bin \
    && mv composer.phar /usr/local/bin/composer \
    && chmod +x /usr/local/bin/composer


COPY docker /tmp/docker/

ENV PHP_ENV_FILE .env
ENV SERVER_PORT 8080
ENV CODE_PATH /app

USER root
WORKDIR /app
ENTRYPOINT ["/app/entrypoint.sh"]
CMD ["start"]
