<?php
/**
 * TOP API: cainiao.cloudprint.mystdtemplates.get request
 * 
 * <AUTHOR> create
 * @since 1.0, 2018.07.26
 */
class CainiaoCloudprintMystdtemplatesGetRequest
{
	
	private $apiParas = array();
	
	public function getApiMethodName()
	{
		return "cainiao.cloudprint.mystdtemplates.get";
	}
	
	public function getApiParas()
	{
		return $this->apiParas;
	}
	
	public function check()
	{
		
	}
	
	public function putOtherTextParam($key, $value) {
		$this->apiParas[$key] = $value;
		$this->$key = $value;
	}
}
