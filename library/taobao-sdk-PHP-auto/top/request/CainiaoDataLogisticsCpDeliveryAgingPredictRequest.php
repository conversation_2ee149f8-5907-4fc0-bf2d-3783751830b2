<?php
/**
 * TOP API: cainiao.data.logistics.cp.delivery.aging.predict request
 * 
 * <AUTHOR> create
 * @since 1.0, 2021.11.24
 */
class CainiaoDataLogisticsCpDeliveryAgingPredictRequest
{
	/** 
	 * 物流公司id
	 **/
	private $cpId;
	
	/** 
	 * 自己输入的详细收货地址
	 **/
	private $recAddr;
	
	/** 
	 * 收货城市
	 **/
	private $recCityName;
	
	/** 
	 * 收货区
	 **/
	private $recCountyName;
	
	/** 
	 * 收货省
	 **/
	private $recProvName;
	
	/** 
	 * 第四级，一般是收货街道
	 **/
	private $recTownName;
	
	/** 
	 * 自己输入的详细发货地址
	 **/
	private $sendAddr;
	
	/** 
	 * 发货城市
	 **/
	private $sendCityName;
	
	/** 
	 * 发货区
	 **/
	private $sendCountyName;
	
	/** 
	 * 发货省
	 **/
	private $sendProvName;
	
	private $apiParas = array();
	
	public function setCpId($cpId)
	{
		$this->cpId = $cpId;
		$this->apiParas["cp_id"] = $cpId;
	}

	public function getCpId()
	{
		return $this->cpId;
	}

	public function setRecAddr($recAddr)
	{
		$this->recAddr = $recAddr;
		$this->apiParas["rec_addr"] = $recAddr;
	}

	public function getRecAddr()
	{
		return $this->recAddr;
	}

	public function setRecCityName($recCityName)
	{
		$this->recCityName = $recCityName;
		$this->apiParas["rec_city_name"] = $recCityName;
	}

	public function getRecCityName()
	{
		return $this->recCityName;
	}

	public function setRecCountyName($recCountyName)
	{
		$this->recCountyName = $recCountyName;
		$this->apiParas["rec_county_name"] = $recCountyName;
	}

	public function getRecCountyName()
	{
		return $this->recCountyName;
	}

	public function setRecProvName($recProvName)
	{
		$this->recProvName = $recProvName;
		$this->apiParas["rec_prov_name"] = $recProvName;
	}

	public function getRecProvName()
	{
		return $this->recProvName;
	}

	public function setRecTownName($recTownName)
	{
		$this->recTownName = $recTownName;
		$this->apiParas["rec_town_name"] = $recTownName;
	}

	public function getRecTownName()
	{
		return $this->recTownName;
	}

	public function setSendAddr($sendAddr)
	{
		$this->sendAddr = $sendAddr;
		$this->apiParas["send_addr"] = $sendAddr;
	}

	public function getSendAddr()
	{
		return $this->sendAddr;
	}

	public function setSendCityName($sendCityName)
	{
		$this->sendCityName = $sendCityName;
		$this->apiParas["send_city_name"] = $sendCityName;
	}

	public function getSendCityName()
	{
		return $this->sendCityName;
	}

	public function setSendCountyName($sendCountyName)
	{
		$this->sendCountyName = $sendCountyName;
		$this->apiParas["send_county_name"] = $sendCountyName;
	}

	public function getSendCountyName()
	{
		return $this->sendCountyName;
	}

	public function setSendProvName($sendProvName)
	{
		$this->sendProvName = $sendProvName;
		$this->apiParas["send_prov_name"] = $sendProvName;
	}

	public function getSendProvName()
	{
		return $this->sendProvName;
	}

	public function getApiMethodName()
	{
		return "cainiao.data.logistics.cp.delivery.aging.predict";
	}
	
	public function getApiParas()
	{
		return $this->apiParas;
	}
	
	public function check()
	{
		
		RequestCheckUtil::checkNotNull($this->cpId,"cpId");
		RequestCheckUtil::checkNotNull($this->recAddr,"recAddr");
		RequestCheckUtil::checkNotNull($this->recCityName,"recCityName");
		RequestCheckUtil::checkNotNull($this->recCountyName,"recCountyName");
		RequestCheckUtil::checkNotNull($this->recProvName,"recProvName");
		RequestCheckUtil::checkNotNull($this->recTownName,"recTownName");
		RequestCheckUtil::checkNotNull($this->sendAddr,"sendAddr");
		RequestCheckUtil::checkNotNull($this->sendCityName,"sendCityName");
		RequestCheckUtil::checkNotNull($this->sendCountyName,"sendCountyName");
		RequestCheckUtil::checkNotNull($this->sendProvName,"sendProvName");
	}
	
	public function putOtherTextParam($key, $value) {
		$this->apiParas[$key] = $value;
		$this->$key = $value;
	}
}
