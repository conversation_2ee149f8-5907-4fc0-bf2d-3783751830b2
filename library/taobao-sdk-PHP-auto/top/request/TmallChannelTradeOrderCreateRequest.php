<?php
/**
 * TOP API: tmall.channel.trade.order.create request
 * 
 * <AUTHOR> create
 * @since 1.0, 2018.07.25
 */
class TmallChannelTradeOrderCreateRequest
{
	/** 
	 * 入参
	 **/
	private $param0;
	
	private $apiParas = array();
	
	public function setParam0($param0)
	{
		$this->param0 = $param0;
		$this->apiParas["param0"] = $param0;
	}

	public function getParam0()
	{
		return $this->param0;
	}

	public function getApiMethodName()
	{
		return "tmall.channel.trade.order.create";
	}
	
	public function getApiParas()
	{
		return $this->apiParas;
	}
	
	public function check()
	{
		
	}
	
	public function putOtherTextParam($key, $value) {
		$this->apiParas[$key] = $value;
		$this->$key = $value;
	}
}
