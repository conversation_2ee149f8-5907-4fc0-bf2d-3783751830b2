<?php
/**
 * TOP API: taobao.rdc.aligenius.account.validate request
 * 
 * <AUTHOR> create
 * @since 1.0, 2018.07.25
 */
class RdcAligeniusAccountValidateRequest
{
	
	private $apiParas = array();
	
	public function getApiMethodName()
	{
		return "taobao.rdc.aligenius.account.validate";
	}
	
	public function getApiParas()
	{
		return $this->apiParas;
	}
	
	public function check()
	{
		
	}
	
	public function putOtherTextParam($key, $value) {
		$this->apiParas[$key] = $value;
		$this->$key = $value;
	}
}
